#!/usr/bin/env python3
"""
验证结果对比分析

对比AI验证结果与原始预设答案，分析差异和改进效果
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict
import numpy as np
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ValidationComparator:
    """验证结果对比器"""
    
    def __init__(self):
        self.original_results = {}
        self.ai_validation_results = {}
    
    def load_original_test_results(self, strategies_results: Dict):
        """加载原始策略测试结果"""
        self.original_results = strategies_results
    
    def load_ai_validation_results(self, ai_results_file: str):
        """加载AI验证结果"""
        try:
            with open(ai_results_file, 'r', encoding='utf-8') as f:
                self.ai_validation_results = json.load(f)
            print(f"✅ 成功加载AI验证结果: {len(self.ai_validation_results)} 个案例")
        except Exception as e:
            print(f"❌ 加载AI验证结果失败: {e}")
    
    def create_new_test_dataset(self, original_dataset_file: str, output_file: str):
        """基于AI验证结果创建新的测试数据集"""
        
        # 加载原始数据集
        with open(original_dataset_file, 'r', encoding='utf-8') as f:
            original_dataset = json.load(f)
        
        # 创建案例ID到AI验证结果的映射
        ai_results_map = {r['case_id']: r for r in self.ai_validation_results if 'error' not in r}
        
        # 更新数据集
        updated_dataset = []
        changes_count = 0
        
        for case in original_dataset:
            case_id = case.get('id')
            
            if case_id in ai_results_map:
                ai_result = ai_results_map[case_id]
                
                # 检查AI验证结果是否有足够的置信度
                if (ai_result.get('confidence', 0) >= 0.6 and 
                    ai_result.get('agreement_rate', 0) >= 0.6):
                    
                    # 更新期望结果
                    new_expected = ai_result.get('ai_consensus_winner', '')
                    old_expected = case.get('expected_winner_id', '')
                    
                    if new_expected and new_expected != old_expected:
                        case['expected_winner_id'] = new_expected
                        case['validation_source'] = 'ai_consensus'
                        case['ai_confidence'] = ai_result.get('confidence')
                        case['ai_agreement_rate'] = ai_result.get('agreement_rate')
                        case['original_expected'] = old_expected
                        changes_count += 1
                    else:
                        case['validation_source'] = 'original_confirmed'
                else:
                    case['validation_source'] = 'original_low_confidence'
            else:
                case['validation_source'] = 'original_no_ai_data'
            
            updated_dataset.append(case)
        
        # 保存新数据集
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(updated_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 新测试数据集已保存: {output_file}")
        print(f"📊 更新了 {changes_count} 个案例的期望结果")
        
        return updated_dataset
    
    def run_updated_strategy_test(self, updated_dataset: List[Dict]):
        """使用更新后的数据集重新测试策略"""
        
        # 这里需要导入原来的策略测试代码
        from clean_strategy_experiment import (
            OptimizedAdaptiveResolver,
            resolve_conflict_weight_first,
            resolve_conflict_latest_first,
            run_strategy_test
        )
        
        print("\n🔄 使用AI验证后的数据集重新测试策略...")
        
        # 初始化策略
        adaptive_resolver = OptimizedAdaptiveResolver()
        
        strategies = [
            (resolve_conflict_weight_first, "Weight-First"),
            (resolve_conflict_latest_first, "Latest-First"),
            (adaptive_resolver, "自适应Hybrid")
        ]
        
        # 运行测试
        updated_results = []
        for strategy_func, strategy_name in strategies:
            result = run_strategy_test(updated_dataset, strategy_func, strategy_name)
            updated_results.append(result)
        
        return updated_results
    
    def compare_results(self, original_results: List[Dict], updated_results: List[Dict]):
        """对比原始结果和更新后的结果"""
        
        print("\n📊 策略性能对比 (原始 vs AI验证后)")
        print("=" * 60)
        
        comparison_data = []
        
        for i, (orig, updated) in enumerate(zip(original_results, updated_results)):
            strategy_name = orig['strategy']
            orig_acc = orig['accuracy']
            updated_acc = updated['accuracy']
            improvement = updated_acc - orig_acc
            
            comparison_data.append({
                'strategy': strategy_name,
                'original_accuracy': orig_acc,
                'updated_accuracy': updated_acc,
                'improvement': improvement,
                'improvement_pct': improvement * 100
            })
            
            print(f"{strategy_name:20} | {orig_acc:.3f} → {updated_acc:.3f} | {improvement:+.3f} ({improvement*100:+.1f}%)")
        
        return comparison_data
    
    def create_comparison_visualization(self, comparison_data: List[Dict]):
        """创建对比可视化"""
        
        strategies = [d['strategy'] for d in comparison_data]
        original_acc = [d['original_accuracy'] for d in comparison_data]
        updated_acc = [d['updated_accuracy'] for d in comparison_data]
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准确率对比
        x = np.arange(len(strategies))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, original_acc, width, label='原始预设答案', alpha=0.8, color='#FF6B6B')
        bars2 = ax1.bar(x + width/2, updated_acc, width, label='AI验证答案', alpha=0.8, color='#32CD32')
        
        ax1.set_xlabel('策略')
        ax1.set_ylabel('准确率')
        ax1.set_title('策略准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(strategies)
        ax1.legend()
        ax1.set_ylim(0, 1)
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom')
        
        # 改进幅度
        improvements = [d['improvement'] for d in comparison_data]
        colors = ['green' if imp > 0 else 'red' if imp < 0 else 'gray' for imp in improvements]
        
        bars3 = ax2.bar(strategies, improvements, color=colors, alpha=0.7)
        ax2.set_xlabel('策略')
        ax2.set_ylabel('准确率改进')
        ax2.set_title('AI验证带来的改进')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 添加数值标签
        for bar, imp in zip(bars3, improvements):
            height = bar.get_height()
            label = f'+{imp:.3f}' if imp > 0 else f'{imp:.3f}'
            y_pos = height + 0.005 if height >= 0 else height - 0.01
            ax2.text(bar.get_x() + bar.get_width()/2., y_pos,
                    label, ha='center', va='bottom' if height >= 0 else 'top')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'validation_comparison_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 对比图表已保存: {filename}")
    
    def analyze_disagreement_cases(self):
        """分析AI与原始答案不一致的案例"""
        
        disagreement_cases = []
        
        for ai_result in self.ai_validation_results:
            if 'error' not in ai_result and not ai_result.get('agrees_with_original', True):
                disagreement_cases.append(ai_result)
        
        if not disagreement_cases:
            print("✅ 所有案例AI验证都与原始答案一致")
            return
        
        print(f"\n🔍 分析 {len(disagreement_cases)} 个不一致案例:")
        print("=" * 50)
        
        # 按冲突类型分析
        by_type = {}
        for case in disagreement_cases:
            conflict_type = case.get('conflict_type', 'unknown')
            if conflict_type not in by_type:
                by_type[conflict_type] = []
            by_type[conflict_type].append(case)
        
        for conflict_type, cases in by_type.items():
            print(f"\n{conflict_type} ({len(cases)}个案例):")
            for case in cases[:3]:  # 显示前3个
                print(f"  • {case.get('case_name', 'N/A')}")
                print(f"    AI选择: {case.get('ai_consensus_choice')} (置信度: {case.get('confidence', 0):.2f})")
                print(f"    一致率: {case.get('agreement_rate', 0):.2f}")
        
        return disagreement_cases

def main():
    """主函数"""
    
    print("🔍 AI验证结果对比分析")
    print("=" * 50)
    
    # 创建对比器
    comparator = ValidationComparator()
    
    # 加载AI验证结果
    ai_results_file = input("请输入AI验证结果文件名 (如: ai_validation_results_20241201_143022.json): ")
    if not ai_results_file:
        print("❌ 未指定AI验证结果文件")
        return
    
    comparator.load_ai_validation_results(ai_results_file)
    
    # 分析不一致案例
    comparator.analyze_disagreement_cases()
    
    # 创建新的测试数据集
    print("\n📝 基于AI验证结果创建新的测试数据集...")
    updated_dataset = comparator.create_new_test_dataset(
        'lhasa_conflict_test_dataset.json',
        f'lhasa_conflict_test_dataset_ai_validated_{datetime.now().strftime("%Y%m%d")}.json'
    )
    
    # 重新测试策略
    updated_results = comparator.run_updated_strategy_test(updated_dataset)
    
    # 加载原始结果进行对比
    print("\n📊 对比策略性能...")
    
    # 这里需要原始结果，可以重新运行或从文件加载
    # 为了演示，我们假设有原始结果
    original_results = [
        {'strategy': 'Weight-First', 'accuracy': 0.630},
        {'strategy': 'Latest-First', 'accuracy': 0.560},
        {'strategy': '自适应Hybrid', 'accuracy': 0.830}
    ]
    
    # 对比结果
    comparison_data = comparator.compare_results(original_results, updated_results)
    
    # 创建可视化
    comparator.create_comparison_visualization(comparison_data)
    
    print("\n✅ 对比分析完成！")

if __name__ == "__main__":
    main()
