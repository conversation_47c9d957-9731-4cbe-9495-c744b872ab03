#!/usr/bin/env python3
"""
深入分析Hybrid策略的设计问题和在困难场景下的表现

重点分析为什么Latest-First在困难场景下表现比Hybrid更好
"""

import json
from datetime import datetime
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def load_test_dataset():
    """加载测试数据集"""
    with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

def analyze_hybrid_strategy_design():
    """分析当前Hybrid策略的设计"""
    print("🔍 当前Hybrid策略设计分析")
    print("=" * 60)
    
    print("📊 当前权重分配:")
    print("   • 权重 (Weight): 50%")
    print("   • 时间 (Time): 30%") 
    print("   • 内容质量 (Content): 20%")
    
    print("\n🧮 评分计算公式:")
    print("   final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2")
    
    print("\n⏰ 时间评分机制:")
    print("   time_score = max(0, 1 - days_old / 730)  # 2年内线性衰减")
    
    print("\n📝 内容质量评分:")
    print("   content_score = min(1.0, content_length / 100)  # 基于字符长度")
    
    return {
        'weight_ratio': 0.5,
        'time_ratio': 0.3,
        'content_ratio': 0.2,
        'time_decay_days': 730
    }

def simulate_hybrid_decision(conflict1, conflict2, strategy_params):
    """模拟Hybrid策略的决策过程"""
    
    def calculate_score(conflict):
        weight = conflict.get('source', {}).get('weight', 0)
        timestamp = parse_timestamp(conflict.get('timestamp', ''))
        content_length = len(conflict.get('value', ''))
        
        # 时间新鲜度评分
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / strategy_params['time_decay_days'])
        
        # 内容质量评分
        content_score = min(1.0, content_length / 100)
        
        # 综合评分
        final_score = (weight * strategy_params['weight_ratio'] + 
                      time_score * strategy_params['time_ratio'] + 
                      content_score * strategy_params['content_ratio'])
        
        return {
            'weight': weight,
            'time_score': time_score,
            'content_score': content_score,
            'final_score': final_score,
            'days_old': days_old
        }
    
    score1 = calculate_score(conflict1)
    score2 = calculate_score(conflict2)
    
    return score1, score2

def analyze_hard_cases():
    """分析困难案例的特点"""
    test_cases = load_test_dataset()
    hard_cases = [case for case in test_cases if case['difficulty'] == 'hard']
    
    print(f"\n🎯 困难案例分析 (共{len(hard_cases)}个)")
    print("=" * 60)
    
    # 按冲突类型分组
    by_type = defaultdict(list)
    for case in hard_cases:
        by_type[case['conflict_type']].append(case)
    
    print("📊 困难案例分布:")
    for conflict_type, cases in by_type.items():
        print(f"   • {conflict_type}: {len(cases)}个")
    
    # 分析期望结果的模式
    expected_patterns = defaultdict(int)
    for case in hard_cases:
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        
        # 找到期望获胜的冲突
        expected_conflict = None
        other_conflict = None
        for conflict in conflicts:
            if conflict['source']['url'] == expected_id:
                expected_conflict = conflict
            else:
                other_conflict = conflict
        
        if expected_conflict and other_conflict:
            # 分析期望获胜者的特点
            expected_weight = expected_conflict['source']['weight']
            other_weight = other_conflict['source']['weight']
            
            expected_time = parse_timestamp(expected_conflict['timestamp'])
            other_time = parse_timestamp(other_conflict['timestamp'])
            
            if expected_weight > other_weight:
                if expected_time > other_time:
                    expected_patterns['权重高且时间新'] += 1
                else:
                    expected_patterns['权重高但时间旧'] += 1
            else:
                if expected_time > other_time:
                    expected_patterns['权重低但时间新'] += 1
                else:
                    expected_patterns['权重低且时间旧'] += 1
    
    print(f"\n🎯 困难案例期望结果模式:")
    for pattern, count in expected_patterns.items():
        percentage = count / len(hard_cases) * 100
        print(f"   • {pattern}: {count}个 ({percentage:.1f}%)")
    
    return hard_cases, expected_patterns

def analyze_hybrid_failures():
    """分析Hybrid策略失败的具体案例"""
    test_cases = load_test_dataset()
    strategy_params = analyze_hybrid_strategy_design()
    
    print(f"\n❌ Hybrid策略失败案例分析")
    print("=" * 60)
    
    failures = []
    
    for case in test_cases:
        if case['difficulty'] == 'hard':
            conflicts = case['conflicts']
            expected_id = case['expected_winner_id']
            
            if len(conflicts) == 2:
                score1, score2 = simulate_hybrid_decision(conflicts[0], conflicts[1], strategy_params)
                
                # 判断Hybrid策略的选择
                if score1['final_score'] > score2['final_score']:
                    hybrid_choice = conflicts[0]['source']['url']
                else:
                    hybrid_choice = conflicts[1]['source']['url']
                
                # 如果选择错误
                if hybrid_choice != expected_id:
                    failure_info = {
                        'case': case,
                        'hybrid_choice': hybrid_choice,
                        'expected_choice': expected_id,
                        'score1': score1,
                        'score2': score2,
                        'conflicts': conflicts
                    }
                    failures.append(failure_info)
    
    print(f"发现 {len(failures)} 个失败案例")
    
    # 分析失败模式
    failure_patterns = defaultdict(int)
    
    for failure in failures[:10]:  # 分析前10个失败案例
        case = failure['case']
        conflicts = failure['conflicts']
        expected_id = failure['expected_choice']
        
        # 找到期望和实际选择的冲突
        expected_conflict = None
        chosen_conflict = None
        
        for conflict in conflicts:
            if conflict['source']['url'] == expected_id:
                expected_conflict = conflict
            if conflict['source']['url'] == failure['hybrid_choice']:
                chosen_conflict = conflict
        
        if expected_conflict and chosen_conflict:
            expected_weight = expected_conflict['source']['weight']
            chosen_weight = chosen_conflict['source']['weight']
            
            expected_time = parse_timestamp(expected_conflict['timestamp'])
            chosen_time = parse_timestamp(chosen_conflict['timestamp'])
            
            print(f"\n失败案例: {case['name']}")
            print(f"   冲突类型: {case['conflict_type']}")
            print(f"   期望选择: 权重{expected_weight:.2f}, 时间{expected_time.strftime('%Y-%m-%d')}")
            print(f"   实际选择: 权重{chosen_weight:.2f}, 时间{chosen_time.strftime('%Y-%m-%d')}")
            print(f"   解释: {case['explanation']}")
            
            # 分析失败原因
            if chosen_weight > expected_weight and chosen_time < expected_time:
                failure_patterns['选择了权重高但时间旧的'] += 1
                print(f"   失败原因: Hybrid过度重视权重，忽略了时效性")
            elif chosen_weight < expected_weight and chosen_time > expected_time:
                failure_patterns['选择了权重低但时间新的'] += 1
                print(f"   失败原因: Hybrid过度重视时效性，忽略了权威性")
            else:
                failure_patterns['其他原因'] += 1
    
    print(f"\n📊 失败模式统计:")
    for pattern, count in failure_patterns.items():
        print(f"   • {pattern}: {count}个")
    
    return failures

def analyze_latest_first_success():
    """分析Latest-First在困难场景成功的原因"""
    test_cases = load_test_dataset()
    hard_cases = [case for case in test_cases if case['difficulty'] == 'hard']
    
    print(f"\n✅ Latest-First在困难场景成功分析")
    print("=" * 60)
    
    latest_successes = 0
    time_dominant_successes = 0
    
    for case in hard_cases:
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        
        if len(conflicts) == 2:
            # Latest-First策略：选择时间最新的
            latest_conflict = max(conflicts, key=lambda x: parse_timestamp(x.get('timestamp', '')))
            latest_choice = latest_conflict['source']['url']
            
            if latest_choice == expected_id:
                latest_successes += 1
                
                # 分析成功的原因
                other_conflict = conflicts[0] if conflicts[1] == latest_conflict else conflicts[1]
                
                latest_time = parse_timestamp(latest_conflict['timestamp'])
                other_time = parse_timestamp(other_conflict['timestamp'])
                time_diff = (latest_time - other_time).days
                
                latest_weight = latest_conflict['source']['weight']
                other_weight = other_conflict['source']['weight']
                
                print(f"\n成功案例: {case['name']}")
                print(f"   时间差: {time_diff}天")
                print(f"   权重差: {latest_weight:.2f} vs {other_weight:.2f}")
                print(f"   解释: {case['explanation']}")
                
                if '时效性更重要' in case['explanation'] or '应选择更新的信息' in case['explanation']:
                    time_dominant_successes += 1
    
    success_rate = latest_successes / len(hard_cases)
    print(f"\n📊 Latest-First在困难场景成功率: {success_rate:.3f} ({latest_successes}/{len(hard_cases)})")
    print(f"📊 其中时效性主导的成功: {time_dominant_successes}个")
    
    return latest_successes, time_dominant_successes

def propose_improved_hybrid():
    """提出改进的Hybrid策略"""
    print(f"\n💡 Hybrid策略改进建议")
    print("=" * 60)
    
    print("🔍 问题诊断:")
    print("   1. 当前权重分配过度偏向权威性 (50%)")
    print("   2. 时间评分机制可能不够敏感")
    print("   3. 内容质量评分过于简单")
    print("   4. 缺乏对不同场景的自适应调整")
    
    print("\n🛠️ 改进方案:")
    print("   方案1: 动态权重调整")
    print("      • 根据时间差动态调整权重和时间的比例")
    print("      • 时间差 > 300天: 时间权重提升到50%")
    print("      • 时间差 < 100天: 权重权重提升到60%")
    
    print("\n   方案2: 场景感知策略")
    print("      • 检测冲突类型，使用不同的评分策略")
    print("      • 时间主导场景: 时间权重70%")
    print("      • 权重主导场景: 权重权重70%")
    
    print("\n   方案3: 阈值决策机制")
    print("      • 当权重差异 > 0.3时，优先考虑权威性")
    print("      • 当时间差异 > 365天时，优先考虑时效性")
    print("      • 否则使用平衡策略")
    
    # 提供具体的改进代码示例
    improved_code = '''
def resolve_conflict_improved_hybrid(conflicts):
    """改进的混合策略"""
    if len(conflicts) != 2:
        return conflicts[0] if conflicts else {}
    
    c1, c2 = conflicts
    
    # 计算基本指标
    weight_diff = abs(c1['source']['weight'] - c2['source']['weight'])
    time1 = parse_timestamp(c1['timestamp'])
    time2 = parse_timestamp(c2['timestamp'])
    time_diff_days = abs((time1 - time2).days)
    
    # 阈值决策
    if weight_diff > 0.3:  # 权威性差异显著
        return max(conflicts, key=lambda x: x['source']['weight'])
    elif time_diff_days > 365:  # 时间差异显著
        return max(conflicts, key=lambda x: parse_timestamp(x['timestamp']))
    else:
        # 动态权重平衡
        if time_diff_days > 180:
            weight_ratio, time_ratio = 0.3, 0.6  # 偏向时效性
        else:
            weight_ratio, time_ratio = 0.6, 0.3  # 偏向权威性
        
        # 计算综合评分
        scores = []
        for conflict in conflicts:
            weight_score = conflict['source']['weight']
            time_score = calculate_time_score(conflict['timestamp'])
            final_score = weight_score * weight_ratio + time_score * time_ratio
            scores.append((final_score, conflict))
        
        return max(scores, key=lambda x: x[0])[1]
'''
    
    print(f"\n💻 改进代码示例:")
    print(improved_code)

def main():
    """主函数"""
    print("🔬 Hybrid策略深度分析：为什么在困难场景表现不如Latest-First")
    print("=" * 80)
    
    # 1. 分析当前策略设计
    strategy_params = analyze_hybrid_strategy_design()
    
    # 2. 分析困难案例特点
    hard_cases, expected_patterns = analyze_hard_cases()
    
    # 3. 分析Hybrid失败案例
    failures = analyze_hybrid_failures()
    
    # 4. 分析Latest-First成功原因
    latest_successes, time_dominant_successes = analyze_latest_first_success()
    
    # 5. 提出改进建议
    propose_improved_hybrid()
    
    print(f"\n🎯 核心结论:")
    print(f"=" * 60)
    print(f"1. 困难场景中有大量时效性主导的案例")
    print(f"2. 当前Hybrid策略过度重视权威性 (50%权重)")
    print(f"3. Latest-First在时效性场景天然优势明显")
    print(f"4. 需要动态调整策略或使用阈值决策机制")
    
    print(f"\n💡 关键洞察:")
    print(f"   困难场景往往是权威性和时效性的真正冲突")
    print(f"   Latest-First的简单策略在这种冲突中反而更有效")
    print(f"   Hybrid需要更智能的权衡机制，而不是固定权重")

if __name__ == "__main__":
    main()
