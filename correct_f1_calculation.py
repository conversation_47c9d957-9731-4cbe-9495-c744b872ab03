#!/usr/bin/env python3
"""
正确的F1分数计算方法

在冲突解决任务中，F1分数的计算需要明确定义：
1. 正例：选择了正确的冲突解决方案
2. 负例：选择了错误的冲突解决方案
3. 精确率：在所有做出的决策中，正确决策的比例
4. 召回率：在所有应该做出的正确决策中，实际做出的比例
"""

import json
import numpy as np
from typing import Dict, List, Tuple
from conflict_experiment import run_conflict_resolution_experiment

def calculate_correct_f1_scores():
    """正确计算F1分数"""
    
    print("🔍 重新计算F1分数...")
    print("="*60)
    
    # 运行原始实验获取详细结果
    results = run_conflict_resolution_experiment()
    test_results = results["test_results"]
    
    # 为每个策略计算精确的精确率、召回率和F1分数
    strategies = ["weight_first", "latest_first", "hybrid"]
    strategy_names = ["Weight-First", "Latest-First", "Hybrid"]
    
    f1_results = {}
    
    for i, strategy in enumerate(strategies):
        print(f"\n📊 计算 {strategy_names[i]} 的F1分数:")
        print("-" * 40)
        
        # 统计混淆矩阵的各项
        true_positives = 0   # 正确选择了最优解决方案
        false_positives = 0  # 错误地认为选择了最优方案（实际选错了）
        false_negatives = 0  # 错误地没有选择最优方案（应该选对但选错了）
        true_negatives = 0   # 正确地没有选择错误方案（这在我们的场景中不太适用）
        
        for test_case in test_results:
            strategy_result = test_case[strategy]  # True表示该策略选择正确
            
            if strategy_result:
                true_positives += 1    # 正确选择了最优方案
            else:
                false_negatives += 1   # 应该选择最优方案但没有选择
                false_positives += 1   # 选择了非最优方案
        
        # 计算精确率和召回率
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        
        # 计算F1分数
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        # 计算准确率（作为对比）
        accuracy = true_positives / len(test_results)
        
        f1_results[strategy] = {
            "strategy_name": strategy_names[i],
            "true_positives": true_positives,
            "false_positives": false_positives,
            "false_negatives": false_negatives,
            "precision": precision,
            "recall": recall,
            "f1_score": f1_score,
            "accuracy": accuracy,
            "total_cases": len(test_results)
        }
        
        print(f"真正例 (TP): {true_positives}")
        print(f"假正例 (FP): {false_positives}")
        print(f"假负例 (FN): {false_negatives}")
        print(f"精确率 (Precision): {precision:.3f}")
        print(f"召回率 (Recall): {recall:.3f}")
        print(f"F1分数: {f1_score:.3f}")
        print(f"准确率 (对比): {accuracy:.3f}")
    
    return f1_results

def analyze_f1_differences(f1_results):
    """分析F1分数的差异和含义"""
    
    print("\n" + "="*60)
    print("📈 F1分数分析")
    print("="*60)
    
    print("\n📊 各策略F1分数对比:")
    print("-" * 40)
    
    for strategy, data in f1_results.items():
        print(f"{data['strategy_name']:15} | F1: {data['f1_score']:.3f} | 精确率: {data['precision']:.3f} | 召回率: {data['recall']:.3f}")
    
    # 找出最佳策略
    best_f1_strategy = max(f1_results.items(), key=lambda x: x[1]['f1_score'])
    best_accuracy_strategy = max(f1_results.items(), key=lambda x: x[1]['accuracy'])
    
    print(f"\n🏆 最佳F1分数: {best_f1_strategy[1]['strategy_name']} ({best_f1_strategy[1]['f1_score']:.3f})")
    print(f"🏆 最佳准确率: {best_accuracy_strategy[1]['strategy_name']} ({best_accuracy_strategy[1]['accuracy']:.3f})")
    
    # 分析F1分数与准确率的关系
    print(f"\n🔍 F1分数 vs 准确率分析:")
    print("-" * 40)
    
    for strategy, data in f1_results.items():
        f1_acc_diff = data['f1_score'] - data['accuracy']
        print(f"{data['strategy_name']:15} | F1-准确率差值: {f1_acc_diff:+.3f}")
        
        if abs(f1_acc_diff) < 0.001:
            print(f"                | → F1分数与准确率基本相等")
        elif f1_acc_diff > 0:
            print(f"                | → F1分数高于准确率，精确率和召回率平衡较好")
        else:
            print(f"                | → F1分数低于准确率，存在精确率召回率不平衡")

def explain_f1_calculation():
    """解释F1分数计算的原理"""
    
    print("\n" + "="*60)
    print("📚 F1分数计算原理解释")
    print("="*60)
    
    explanation = """
在冲突解决任务中，F1分数的计算定义：

🎯 任务定义：
- 目标：为每个冲突选择最优的解决方案
- 成功：选择了预期的最优方案
- 失败：选择了非最优方案

📊 混淆矩阵定义：
- 真正例 (TP)：正确选择了最优方案的案例数
- 假正例 (FP)：错误选择了非最优方案的案例数  
- 假负例 (FN)：应该选择最优方案但选错了的案例数
- 真负例 (TN)：在我们的任务中不适用（每个冲突都必须做决策）

📈 指标计算：
- 精确率 (Precision) = TP / (TP + FP)
  → 在所有做出的决策中，正确决策的比例
  
- 召回率 (Recall) = TP / (TP + FN)  
  → 在所有应该做出的正确决策中，实际做出的比例
  
- F1分数 = 2 × (Precision × Recall) / (Precision + Recall)
  → 精确率和召回率的调和平均数

🔍 在我们的冲突解决任务中：
- 每个测试案例都必须做出决策（没有"不决策"的选项）
- 因此 TP + FN = 总案例数（所有应该做出正确决策的案例）
- 因此 TP + FP = 总案例数（所有实际做出的决策）
- 所以 Precision = Recall = Accuracy = TP / 总案例数
- 因此 F1分数 = Accuracy

💡 结论：
在我们的冲突解决任务设定下，F1分数实际上等于准确率！
这是因为每个冲突都必须做出决策，不存在"拒绝决策"的情况。
"""
    
    print(explanation)

def create_corrected_f1_report(f1_results):
    """创建修正的F1分数报告"""
    
    report = f"""
# F1分数计算修正报告

## 🎯 问题发现
在原始实验中，F1分数的计算存在概念混淆：
- 错误地假设精确率等于准确率
- 错误地假设召回率为1.0
- 没有正确定义正负例

## 📊 正确的F1分数计算结果

### 各策略详细指标
"""
    
    for strategy, data in f1_results.items():
        report += f"""
#### {data['strategy_name']}
- **真正例 (TP)**: {data['true_positives']}
- **假正例 (FP)**: {data['false_positives']}  
- **假负例 (FN)**: {data['false_negatives']}
- **精确率**: {data['precision']:.3f}
- **召回率**: {data['recall']:.3f}
- **F1分数**: {data['f1_score']:.3f}
- **准确率**: {data['accuracy']:.3f}
"""
    
    # 找出最佳策略
    best_strategy = max(f1_results.items(), key=lambda x: x[1]['f1_score'])
    
    report += f"""
## 🏆 结论

### 最佳策略
**{best_strategy[1]['strategy_name']}** 在F1分数上表现最佳：{best_strategy[1]['f1_score']:.3f}

### 重要发现
在冲突解决任务中，由于每个冲突都必须做出决策（不存在拒绝决策的选项），
因此：
- 精确率 = 召回率 = 准确率 = 正确决策数 / 总决策数
- F1分数 = 准确率

这意味着在我们的实验设定下，**F1分数实际上就等于准确率**。

### 建议
1. 在冲突解决任务中，准确率已经是最直接有效的评估指标
2. 如果要使F1分数更有意义，需要重新设计任务，允许"拒绝决策"选项
3. 或者改为多分类任务，评估选择不同冲突解决策略的效果

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('f1_score_correction_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 保存详细数据
    with open('f1_score_detailed_results.json', 'w', encoding='utf-8') as f:
        json.dump(f1_results, f, ensure_ascii=False, indent=2)
    
    print("✅ F1分数修正报告已生成:")
    print("📄 详细报告: f1_score_correction_report.md")
    print("📊 详细数据: f1_score_detailed_results.json")

def main():
    """主函数"""
    print("🔧 F1分数计算修正")
    print("="*60)
    print("目标：正确计算和理解F1分数在冲突解决任务中的含义")
    print("="*60)
    
    # 1. 计算正确的F1分数
    f1_results = calculate_correct_f1_scores()
    
    # 2. 分析F1分数差异
    analyze_f1_differences(f1_results)
    
    # 3. 解释F1分数计算原理
    explain_f1_calculation()
    
    # 4. 生成修正报告
    create_corrected_f1_report(f1_results)
    
    print(f"\n🎉 F1分数分析完成！")
    print("="*60)
    print("💡 关键发现：在我们的冲突解决任务中，F1分数 = 准确率")
    print("💡 原因：每个冲突都必须做决策，不存在拒绝决策的情况")
    print("💡 建议：使用准确率作为主要评估指标更直接有效")

if __name__ == "__main__":
    from datetime import datetime
    main()
