#!/usr/bin/env python3
"""
知识图谱演化质量评估实验框架

该模块实现了多策略代理与传统单策略在知识图谱动态演化中的对比实验，
包括准确性、完整性、冲突解决能力和演化效率的评估。

实验设计：
1. 数据集构建：黄金标准数据集 + 模拟多源动态数据
2. 对比基准：多策略代理 vs 传统单策略
3. 评估指标：准确率、覆盖率、冲突解决率、演化效率
4. 可视化输出：雷达图、时间序列图
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon
import seaborn as sns

from config import Config
from neo4j_connection import Neo4jConnection
from knowledge_graph_updater import KnowledgeGraphUpdater
from text_processor import process_json_chunk
from conflict_resolution import ConflictResolver
from single_strategy_baseline import SingleStrategyProcessor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

@dataclass
class ExperimentMetrics:
    """实验评估指标"""
    accuracy: float = 0.0          # 准确率
    coverage: float = 0.0          # 覆盖率
    conflict_resolution_rate: float = 0.0  # 冲突解决率
    evolution_efficiency: float = 0.0      # 演化效率（实体/秒）
    precision: float = 0.0         # 精确率
    recall: float = 0.0           # 召回率
    f1_score: float = 0.0         # F1分数
    processing_time: float = 0.0   # 处理时间（秒）
    entities_created: int = 0      # 创建的实体数
    relationships_created: int = 0  # 创建的关系数
    conflicts_detected: int = 0    # 检测到的冲突数
    conflicts_resolved: int = 0    # 解决的冲突数

@dataclass
class ExperimentResult:
    """实验结果"""
    strategy_name: str
    metrics: ExperimentMetrics
    timestamp: str
    details: Dict[str, Any]

class GoldenStandardDataset:
    """黄金标准数据集"""
    
    def __init__(self, data_path: str = "data/golden_standard.json"):
        self.data_path = data_path
        self.entities = {}
        self.relationships = {}
        self.load_dataset()
    
    def load_dataset(self):
        """加载黄金标准数据集"""
        if os.path.exists(self.data_path):
            try:
                with open(self.data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.entities = data.get('entities', {})
                    self.relationships = data.get('relationships', {})
                logger.info(f"加载黄金标准数据集: {len(self.entities)} 个实体, {len(self.relationships)} 个关系")
            except Exception as e:
                logger.error(f"加载黄金标准数据集失败: {e}")
                self.create_default_dataset()
        else:
            logger.warning("黄金标准数据集不存在，创建默认数据集")
            self.create_default_dataset()
    
    def create_default_dataset(self):
        """创建默认的黄金标准数据集"""
        # 基于西藏旅游的权威数据
        self.entities = {
            "布达拉宫": {
                "name": "布达拉宫",
                "category": "历史建筑",
                "location": "拉萨市",
                "description": "世界文化遗产，西藏最著名的宫殿建筑群",
                "visitor_percentage": "85%",
                "ranking": 1,
                "source_type": "government",
                "authority_score": 1.0
            },
            "大昭寺": {
                "name": "大昭寺",
                "category": "宗教建筑",
                "location": "拉萨市",
                "description": "藏传佛教圣地，拉萨的心脏",
                "visitor_percentage": "75%",
                "ranking": 2,
                "source_type": "government",
                "authority_score": 1.0
            },
            "纳木措": {
                "name": "纳木措",
                "category": "自然风光",
                "location": "当雄县",
                "description": "西藏三大圣湖之一，高原明珠",
                "visitor_percentage": "70%",
                "ranking": 1,
                "source_type": "government",
                "authority_score": 1.0
            }
        }
        
        self.relationships = {
            "布达拉宫-拉萨市": {
                "source": "布达拉宫",
                "target": "拉萨市",
                "type": "LOCATED_IN",
                "properties": {"confidence": 1.0}
            },
            "大昭寺-拉萨市": {
                "source": "大昭寺",
                "target": "拉萨市",
                "type": "LOCATED_IN",
                "properties": {"confidence": 1.0}
            },
            "布达拉宫-大昭寺": {
                "source": "布达拉宫",
                "target": "大昭寺",
                "type": "NEARBY",
                "properties": {"distance": "2km", "confidence": 0.9}
            }
        }
        
        self.save_dataset()
    
    def save_dataset(self):
        """保存数据集"""
        os.makedirs(os.path.dirname(self.data_path), exist_ok=True)
        with open(self.data_path, 'w', encoding='utf-8') as f:
            json.dump({
                'entities': self.entities,
                'relationships': self.relationships
            }, f, ensure_ascii=False, indent=2)
        logger.info(f"保存黄金标准数据集到 {self.data_path}")

class MultiSourceDataSimulator:
    """多源数据模拟器"""
    
    def __init__(self, golden_standard: GoldenStandardDataset):
        self.golden_standard = golden_standard
    
    def generate_conflicting_data(self, conflict_rate: float = 0.3) -> List[Dict]:
        """生成包含冲突、冗余和过时信息的模拟数据"""
        simulated_data = []
        
        for entity_name, entity_data in self.golden_standard.entities.items():
            # 生成正确数据
            correct_data = entity_data.copy()
            correct_data.update({
                "pub_timestamp": datetime.now().isoformat(),
                "source_type": "government",
                "source_url": f"http://gov.example.com/{entity_name}"
            })
            simulated_data.append(correct_data)
            
            # 根据冲突率生成冲突数据
            if np.random.random() < conflict_rate:
                conflicting_data = entity_data.copy()
                # 引入冲突
                if "visitor_percentage" in conflicting_data:
                    # 修改访客比例
                    original_pct = float(conflicting_data["visitor_percentage"].strip('%'))
                    conflicting_pct = max(10, original_pct + np.random.randint(-20, 20))
                    conflicting_data["visitor_percentage"] = f"{conflicting_pct}%"
                
                if "description" in conflicting_data:
                    # 修改描述
                    conflicting_data["description"] = conflicting_data["description"] + "（用户补充信息）"
                
                conflicting_data.update({
                    "pub_timestamp": (datetime.now() - timedelta(days=30)).isoformat(),
                    "source_type": "social_media",
                    "source_url": f"http://social.example.com/{entity_name}"
                })
                simulated_data.append(conflicting_data)
        
        return simulated_data

class EvolutionQualityExperiment:
    """知识图谱演化质量评估实验"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.golden_standard = GoldenStandardDataset()
        self.data_simulator = MultiSourceDataSimulator(self.golden_standard)
        self.results = []
    
    async def run_multi_strategy_experiment(self, test_data: List[Dict]) -> ExperimentResult:
        """运行多策略代理实验"""
        logger.info("开始多策略代理实验...")
        start_time = time.time()
        
        # 清空数据库
        await self.clear_database()
        
        # 使用现有的多策略系统处理数据
        updater = KnowledgeGraphUpdater(self.neo4j_conn)
        conflict_resolver = ConflictResolver(updater.crud)
        
        metrics = ExperimentMetrics()
        
        try:
            # 处理测试数据
            result = await process_json_chunk(
                self.neo4j_conn, 
                test_data, 
                datetime.now().isoformat(),
                "experiment",
                {"authority": 0.8}
            )
            
            processing_time = time.time() - start_time
            
            # 计算评估指标
            metrics = await self.calculate_metrics(
                strategy_name="多策略代理",
                processing_time=processing_time,
                result=result
            )
            
            return ExperimentResult(
                strategy_name="多策略代理",
                metrics=metrics,
                timestamp=datetime.now().isoformat(),
                details=result
            )
            
        except Exception as e:
            logger.error(f"多策略代理实验失败: {e}")
            raise
    
    async def run_single_strategy_experiment(self, test_data: List[Dict]) -> ExperimentResult:
        """运行单策略基准实验"""
        logger.info("开始单策略基准实验...")
        start_time = time.time()
        
        # 清空数据库
        await self.clear_database()
        
        metrics = ExperimentMetrics()
        
        try:
            # 使用简单的单策略处理（仅基于规则，无冲突解决）
            result = await self.simple_single_strategy_processing(test_data)
            
            processing_time = time.time() - start_time
            
            # 计算评估指标
            metrics = await self.calculate_metrics(
                strategy_name="单策略基准",
                processing_time=processing_time,
                result=result
            )
            
            return ExperimentResult(
                strategy_name="单策略基准",
                metrics=metrics,
                timestamp=datetime.now().isoformat(),
                details=result
            )
            
        except Exception as e:
            logger.error(f"单策略基准实验失败: {e}")
            raise
    
    async def simple_single_strategy_processing(self, test_data: List[Dict]) -> Dict:
        """简单的单策略处理逻辑"""
        # 使用专门的单策略处理器
        single_processor = SingleStrategyProcessor(self.neo4j_conn)

        try:
            result = await single_processor.process_data(test_data, source_type="experiment")

            return {
                "processed": result.processed,
                "failed": result.failed,
                "relationships_created": result.relationships_created,
                "conflicts_detected": result.conflicts_detected,
                "conflicts_resolved": result.conflicts_resolved
            }
        except Exception as e:
            logger.error(f"单策略处理失败: {e}")
            return {
                "processed": 0,
                "failed": len(test_data),
                "relationships_created": 0,
                "conflicts_detected": 0,
                "conflicts_resolved": 0
            }

    async def calculate_metrics(self, strategy_name: str, processing_time: float, result: Dict) -> ExperimentMetrics:
        """计算评估指标"""
        metrics = ExperimentMetrics()

        try:
            # 从数据库获取当前状态
            with self.neo4j_conn.driver.session() as session:
                # 获取创建的实体数
                entity_result = session.run("MATCH (a:Attraction) RETURN count(a) as count")
                entities_created = entity_result.single()["count"]

                # 获取创建的关系数
                rel_result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
                relationships_created = rel_result.single()["count"]

                # 获取实际创建的实体
                entities_result = session.run("""
                    MATCH (a:Attraction)
                    RETURN a.name as name, a.category as category,
                           a.location as location, a.visitor_percentage as visitor_percentage
                """)
                created_entities = {record["name"]: dict(record) for record in entities_result}

            # 计算准确率：与黄金标准匹配的实体比例
            correct_entities = 0
            for entity_name, entity_data in created_entities.items():
                if entity_name in self.golden_standard.entities:
                    golden_entity = self.golden_standard.entities[entity_name]
                    # 检查关键字段是否匹配
                    if (entity_data.get("category") == golden_entity.get("category") and
                        entity_data.get("location") == golden_entity.get("location")):
                        correct_entities += 1

            accuracy = correct_entities / len(created_entities) if created_entities else 0.0

            # 计算覆盖率：黄金标准中被覆盖的实体比例
            covered_entities = len(set(created_entities.keys()) & set(self.golden_standard.entities.keys()))
            coverage = covered_entities / len(self.golden_standard.entities) if self.golden_standard.entities else 0.0

            # 计算精确率和召回率
            precision = correct_entities / len(created_entities) if created_entities else 0.0
            recall = covered_entities / len(self.golden_standard.entities) if self.golden_standard.entities else 0.0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            # 计算演化效率
            evolution_efficiency = entities_created / processing_time if processing_time > 0 else 0.0

            # 冲突解决率（从结果中获取）
            conflicts_detected = result.get("conflicts_detected", 0)
            conflicts_resolved = result.get("conflicts_resolved", 0)
            conflict_resolution_rate = conflicts_resolved / conflicts_detected if conflicts_detected > 0 else 1.0

            # 更新指标
            metrics.accuracy = accuracy
            metrics.coverage = coverage
            metrics.conflict_resolution_rate = conflict_resolution_rate
            metrics.evolution_efficiency = evolution_efficiency
            metrics.precision = precision
            metrics.recall = recall
            metrics.f1_score = f1_score
            metrics.processing_time = processing_time
            metrics.entities_created = entities_created
            metrics.relationships_created = relationships_created
            metrics.conflicts_detected = conflicts_detected
            metrics.conflicts_resolved = conflicts_resolved

            logger.info(f"{strategy_name} 指标计算完成: 准确率={accuracy:.3f}, 覆盖率={coverage:.3f}, F1={f1_score:.3f}")

        except Exception as e:
            logger.error(f"计算指标失败: {e}")

        return metrics

    async def clear_database(self):
        """清空数据库"""
        try:
            with self.neo4j_conn.driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")

    async def run_complete_experiment(self) -> List[ExperimentResult]:
        """运行完整的对比实验"""
        logger.info("开始知识图谱演化质量评估实验")

        # 生成测试数据
        test_data = self.data_simulator.generate_conflicting_data(conflict_rate=0.4)
        logger.info(f"生成测试数据: {len(test_data)} 条记录")

        results = []

        try:
            # 运行多策略代理实验
            multi_strategy_result = await self.run_multi_strategy_experiment(test_data)
            results.append(multi_strategy_result)

            # 运行单策略基准实验
            single_strategy_result = await self.run_single_strategy_experiment(test_data)
            results.append(single_strategy_result)

            self.results = results

            # 保存实验结果
            await self.save_results()

            # 生成可视化报告
            await self.generate_visualization()

            logger.info("实验完成，结果已保存")

        except Exception as e:
            logger.error(f"实验执行失败: {e}")
            raise

        return results

    async def save_results(self):
        """保存实验结果"""
        results_data = []
        for result in self.results:
            results_data.append({
                "strategy_name": result.strategy_name,
                "metrics": asdict(result.metrics),
                "timestamp": result.timestamp,
                "details": result.details
            })

        results_path = "experiment_results.json"
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)

        logger.info(f"实验结果已保存到 {results_path}")

    async def generate_visualization(self):
        """生成可视化报告"""
        if len(self.results) < 2:
            logger.warning("结果数量不足，无法生成对比图表")
            return

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('知识图谱演化质量评估实验结果', fontsize=16, fontweight='bold')

        # 准备数据
        strategies = [result.strategy_name for result in self.results]
        metrics_data = {
            'accuracy': [result.metrics.accuracy for result in self.results],
            'coverage': [result.metrics.coverage for result in self.results],
            'conflict_resolution_rate': [result.metrics.conflict_resolution_rate for result in self.results],
            'f1_score': [result.metrics.f1_score for result in self.results],
            'evolution_efficiency': [result.metrics.evolution_efficiency for result in self.results],
            'processing_time': [result.metrics.processing_time for result in self.results]
        }

        # 1. 雷达图 - 主要指标对比
        self.create_radar_chart(ax1, strategies, metrics_data)

        # 2. 柱状图 - 准确率和覆盖率对比
        self.create_accuracy_coverage_chart(ax2, strategies, metrics_data)

        # 3. 效率对比图
        self.create_efficiency_chart(ax3, strategies, metrics_data)

        # 4. 综合评分对比
        self.create_comprehensive_score_chart(ax4, strategies, metrics_data)

        plt.tight_layout()
        plt.savefig('experiment_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()

        logger.info("可视化报告已生成: experiment_visualization.png")

    def create_radar_chart(self, ax, strategies, metrics_data):
        """创建雷达图"""
        # 雷达图的指标
        radar_metrics = ['accuracy', 'coverage', 'conflict_resolution_rate', 'f1_score']
        radar_labels = ['准确率', '覆盖率', '冲突解决率', 'F1分数']

        # 创建简化的雷达图（使用柱状图代替）
        x = np.arange(len(radar_metrics))
        width = 0.35

        colors = ['#FF6B6B', '#4ECDC4']

        for i, strategy in enumerate(strategies):
            values = [metrics_data[metric][i] for metric in radar_metrics]
            bars = ax.bar(x + i * width, values, width, label=strategy, color=colors[i], alpha=0.8)

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{height:.3f}', ha='center', va='bottom', fontsize=8)

        ax.set_xlabel('指标')
        ax.set_ylabel('分数')
        ax.set_title('主要指标对比', fontsize=12, fontweight='bold')
        ax.set_xticks(x + width/2)
        ax.set_xticklabels(radar_labels, rotation=45, ha='right')
        ax.legend()
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3)

    def create_accuracy_coverage_chart(self, ax, strategies, metrics_data):
        """创建准确率和覆盖率对比图"""
        x = np.arange(len(strategies))
        width = 0.35

        bars1 = ax.bar(x - width/2, metrics_data['accuracy'], width, label='准确率', color='#FF6B6B', alpha=0.8)
        bars2 = ax.bar(x + width/2, metrics_data['coverage'], width, label='覆盖率', color='#4ECDC4', alpha=0.8)

        ax.set_xlabel('策略')
        ax.set_ylabel('比率')
        ax.set_title('准确率与覆盖率对比')
        ax.set_xticks(x)
        ax.set_xticklabels(strategies)
        ax.legend()
        ax.set_ylim(0, 1.1)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom')

        for bar in bars2:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom')

    def create_efficiency_chart(self, ax, strategies, metrics_data):
        """创建效率对比图"""
        bars = ax.bar(strategies, metrics_data['evolution_efficiency'],
                     color=['#FF6B6B', '#4ECDC4'], alpha=0.8)

        ax.set_xlabel('策略')
        ax.set_ylabel('演化效率 (实体/秒)')
        ax.set_title('演化效率对比')

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.2f}', ha='center', va='bottom')

    def create_comprehensive_score_chart(self, ax, strategies, metrics_data):
        """创建综合评分对比图"""
        # 计算综合评分 (加权平均)
        weights = {'accuracy': 0.3, 'coverage': 0.3, 'conflict_resolution_rate': 0.2, 'f1_score': 0.2}

        comprehensive_scores = []
        for i in range(len(strategies)):
            score = sum(metrics_data[metric][i] * weight for metric, weight in weights.items())
            comprehensive_scores.append(score)

        bars = ax.bar(strategies, comprehensive_scores,
                     color=['#FF6B6B', '#4ECDC4'], alpha=0.8)

        ax.set_xlabel('策略')
        ax.set_ylabel('综合评分')
        ax.set_title('综合评分对比')
        ax.set_ylim(0, 1.1)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom')

async def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('experiment.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    try:
        # 初始化Neo4j连接
        config = Config()
        neo4j_config = config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            neo4j_config["uri"],
            neo4j_config["username"],
            neo4j_config["password"]
        )

        # 验证连接
        if not neo4j_conn.verify_connection():
            raise Exception("Neo4j连接失败")

        # 运行实验
        experiment = EvolutionQualityExperiment(neo4j_conn)
        results = await experiment.run_complete_experiment()

        # 打印结果摘要
        print("\n" + "="*80)
        print("实验结果摘要")
        print("="*80)

        for result in results:
            print(f"\n策略: {result.strategy_name}")
            print(f"准确率: {result.metrics.accuracy:.3f}")
            print(f"覆盖率: {result.metrics.coverage:.3f}")
            print(f"冲突解决率: {result.metrics.conflict_resolution_rate:.3f}")
            print(f"F1分数: {result.metrics.f1_score:.3f}")
            print(f"演化效率: {result.metrics.evolution_efficiency:.2f} 实体/秒")
            print(f"处理时间: {result.metrics.processing_time:.2f} 秒")

        print("\n实验完成！详细结果请查看 experiment_results.json 和 experiment_visualization.png")

    except Exception as e:
        logger.error(f"实验执行失败: {e}")
        raise
    finally:
        if 'neo4j_conn' in locals():
            neo4j_conn.close()

if __name__ == "__main__":
    asyncio.run(main())
