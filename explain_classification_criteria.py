#!/usr/bin/env python3
"""
详细解释测试用例的分类标准

解释冲突类型、难度等级是如何定义和计算的
"""

import json
import statistics
from datetime import datetime
from collections import defaultdict

def load_and_analyze_dataset():
    """加载数据集并分析分类标准"""
    
    try:
        with open('test_dataset_100_complete.json', 'r', encoding='utf-8') as f:
            test_cases = json.load(f)
    except FileNotFoundError:
        print("❌ 数据集文件未找到")
        return []
    
    return test_cases

def analyze_conflict_type_criteria():
    """分析冲突类型的分类标准"""
    
    print("🏷️ 冲突类型分类标准详解")
    print("="*80)
    
    test_cases = load_and_analyze_dataset()
    if not test_cases:
        return
    
    # 按类型分组
    type_groups = defaultdict(list)
    for case in test_cases:
        type_groups[case['conflict_type']].append(case)
    
    print("📊 各冲突类型的定义和特征:")
    
    for conflict_type, cases in type_groups.items():
        print(f"\n🔍 {conflict_type.upper()} ({len(cases)}个案例)")
        print("-" * 60)
        
        # 分析权重差异
        weight_diffs = []
        time_diffs = []
        
        for case in cases:
            conflicts = case['conflicts']
            if len(conflicts) == 2:
                weight1 = conflicts[0]['source']['weight']
                weight2 = conflicts[1]['source']['weight']
                weight_diff = abs(weight1 - weight2)
                weight_diffs.append(weight_diff)
                
                time1 = datetime.fromisoformat(conflicts[0]['timestamp'].replace('Z', '+00:00'))
                time2 = datetime.fromisoformat(conflicts[1]['timestamp'].replace('Z', '+00:00'))
                time_diff = abs((time1 - time2).days)
                time_diffs.append(time_diff)
        
        if weight_diffs and time_diffs:
            print(f"   权重差异: 平均 {statistics.mean(weight_diffs):.3f}, 范围 [{min(weight_diffs):.3f}, {max(weight_diffs):.3f}]")
            print(f"   时间差异: 平均 {statistics.mean(time_diffs):.0f}天, 范围 [{min(time_diffs)}, {max(time_diffs)}]天")
        
        # 显示几个具体例子
        print(f"   具体例子:")
        for i, case in enumerate(cases[:3]):
            conflicts = case['conflicts']
            if len(conflicts) == 2:
                w1, w2 = conflicts[0]['source']['weight'], conflicts[1]['source']['weight']
                t1 = datetime.fromisoformat(conflicts[0]['timestamp'].replace('Z', '+00:00'))
                t2 = datetime.fromisoformat(conflicts[1]['timestamp'].replace('Z', '+00:00'))
                time_diff = abs((t1 - t2).days)
                print(f"     例{i+1}: 权重 {w1:.2f} vs {w2:.2f} (差异{abs(w1-w2):.3f}), 时间差异 {time_diff}天")

def explain_classification_logic():
    """解释分类逻辑"""
    
    print(f"\n🧠 分类逻辑详解")
    print("="*80)
    
    print("📋 基于代码逻辑的分类标准:")
    
    print(f"\n1️⃣ WEIGHT_DOMINANT (权重主导型):")
    print("   定义: 权重差异大，权重因素应该主导决策")
    print("   标准: 高权重源 vs 低权重源")
    print("   权重差异: 通常 > 0.4")
    print("   时间设置: 高权重源较旧，低权重源较新")
    print("   预期获胜者: 高权重源 (权重优势超过时间劣势)")
    print("   例子: 政府网站(0.95, 2021年) vs 个人博客(0.25, 2023年)")
    
    print(f"\n2️⃣ TIME_DOMINANT (时间主导型):")
    print("   定义: 权重相近，时间因素应该主导决策")
    print("   标准: 权重相近的源，但时间差异大")
    print("   权重差异: 通常 < 0.2")
    print("   时间差异: 通常 > 500天")
    print("   预期获胜者: 新时间源 (时间优势超过权重劣势)")
    print("   例子: 旅游网站(0.6, 2021年) vs 旅游网站(0.65, 2023年)")
    
    print(f"\n3️⃣ COMPLEX_TRADEOFF (复杂权衡型):")
    print("   定义: 权重和时间都有中等差异，需要综合权衡")
    print("   标准: 中等权重差异 + 中等时间差异")
    print("   权重差异: 0.2 - 0.4")
    print("   时间差异: 300 - 800天")
    print("   预期获胜者: 根据具体权衡决定")
    print("   决策逻辑: if 权重差异 < 0.2 and 时间差异 > 500天: 选新的")
    print("            else: 选权重高的")
    print("   例子: 旅游局(0.8, 2021年) vs 旅游指南(0.7, 2023年)")
    
    print(f"\n4️⃣ EDGE_CASE (边界情况):")
    print("   定义: 权重和时间都非常接近，难以决策")
    print("   标准: 权重差异和时间差异都很小")
    print("   权重差异: < 0.1")
    print("   时间差异: < 48小时")
    print("   预期获胜者: 内容更详细的")
    print("   例子: 旅游网站(0.75, 2023-06-15) vs 旅游指南(0.75, 2023-06-15)")

def analyze_difficulty_criteria():
    """分析难度等级的分类标准"""
    
    print(f"\n📊 难度等级分类标准")
    print("="*80)
    
    test_cases = load_and_analyze_dataset()
    if not test_cases:
        return
    
    # 按难度分组
    difficulty_groups = defaultdict(list)
    for case in test_cases:
        difficulty_groups[case['difficulty']].append(case)
    
    print("🎯 难度等级定义:")
    
    for difficulty, cases in difficulty_groups.items():
        print(f"\n📈 {difficulty.upper()} ({len(cases)}个案例)")
        print("-" * 40)
        
        # 分析该难度下的特征
        conflict_types = defaultdict(int)
        weight_diffs = []
        
        for case in cases:
            conflict_types[case['conflict_type']] += 1
            
            conflicts = case['conflicts']
            if len(conflicts) == 2:
                weight1 = conflicts[0]['source']['weight']
                weight2 = conflicts[1]['source']['weight']
                weight_diff = abs(weight1 - weight2)
                weight_diffs.append(weight_diff)
        
        print(f"   冲突类型分布: {dict(conflict_types)}")
        if weight_diffs:
            print(f"   平均权重差异: {statistics.mean(weight_diffs):.3f}")
    
    print(f"\n🧠 难度分级逻辑:")
    print("   EASY: 权重差异 > 0.5 的权重主导型冲突")
    print("         决策明确，权重优势明显")
    print("   MEDIUM: 权重差异 0.2-0.5 的冲突，或时间主导型")
    print("           需要一定的权衡判断")
    print("   HARD: 复杂权衡型和边界情况")
    print("         需要综合多个因素，决策困难")

def validate_classification_consistency():
    """验证分类的一致性"""
    
    print(f"\n✅ 分类一致性验证")
    print("="*80)
    
    test_cases = load_and_analyze_dataset()
    if not test_cases:
        return
    
    inconsistencies = []
    
    for case in test_cases:
        conflicts = case['conflicts']
        if len(conflicts) != 2:
            continue
            
        weight1 = conflicts[0]['source']['weight']
        weight2 = conflicts[1]['source']['weight']
        weight_diff = abs(weight1 - weight2)
        
        time1 = datetime.fromisoformat(conflicts[0]['timestamp'].replace('Z', '+00:00'))
        time2 = datetime.fromisoformat(conflicts[1]['timestamp'].replace('Z', '+00:00'))
        time_diff = abs((time1 - time2).days)
        
        conflict_type = case['conflict_type']
        difficulty = case['difficulty']
        
        # 验证分类逻辑
        expected_type = None
        expected_difficulty = None
        
        if weight_diff > 0.4:
            expected_type = "weight_dominant"
            expected_difficulty = "easy" if weight_diff > 0.5 else "medium"
        elif weight_diff < 0.2 and time_diff > 500:
            expected_type = "time_dominant"
            expected_difficulty = "medium"
        elif 0.2 <= weight_diff <= 0.4:
            expected_type = "complex_tradeoff"
            expected_difficulty = "hard"
        elif weight_diff < 0.1 and time_diff < 48:
            expected_type = "edge_case"
            expected_difficulty = "hard"
        
        if expected_type and expected_type != conflict_type:
            inconsistencies.append({
                'case_id': case['id'],
                'actual_type': conflict_type,
                'expected_type': expected_type,
                'weight_diff': weight_diff,
                'time_diff': time_diff
            })
    
    print(f"📊 一致性检查结果:")
    print(f"   总测试用例: {len(test_cases)}")
    print(f"   分类不一致: {len(inconsistencies)}")
    print(f"   一致性率: {(len(test_cases) - len(inconsistencies)) / len(test_cases):.1%}")
    
    if inconsistencies:
        print(f"\n⚠️  发现的不一致案例:")
        for inc in inconsistencies[:5]:  # 只显示前5个
            print(f"   案例{inc['case_id']}: {inc['actual_type']} → 应为 {inc['expected_type']}")
            print(f"     权重差异: {inc['weight_diff']:.3f}, 时间差异: {inc['time_diff']}天")

def main():
    """主函数"""
    
    print("🔍 测试用例分类标准详细解释")
    print("="*80)
    print("解答: 简单/复杂、权重主导/时间主导等分类是如何定义和计算的")
    print("="*80)
    
    # 1. 分析冲突类型标准
    analyze_conflict_type_criteria()
    
    # 2. 解释分类逻辑
    explain_classification_logic()
    
    # 3. 分析难度标准
    analyze_difficulty_criteria()
    
    # 4. 验证分类一致性
    validate_classification_consistency()
    
    print(f"\n🎯 总结")
    print("="*60)
    print("✅ 冲突类型基于权重差异和时间差异的量化标准")
    print("✅ 难度等级基于决策复杂度的分级标准")
    print("✅ 每个分类都有明确的数值界限和逻辑规则")
    print("✅ 分类标准可以被验证和复现")

if __name__ == "__main__":
    main()
