#!/usr/bin/env python3
"""
基于拉萨知识图谱数据生成冲突解决策略测试集

该脚本从真实的拉萨景点数据中提取信息，生成各种类型的冲突场景，
用于测试Weight-First、Latest-First和Hybrid策略的性能。
"""

import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any
import copy

def load_lhasa_data():
    """加载拉萨知识图谱数据"""
    try:
        with open('data/lhasa_knowledge_graph.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data['nodes']
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return []

def create_source_types():
    """定义不同类型的信息源及其权重"""
    return {
        "government": {"weight": 0.95, "name": "政府官方"},
        "tourism_bureau": {"weight": 0.90, "name": "旅游局"},
        "official_site": {"weight": 0.85, "name": "官方网站"},
        "academic": {"weight": 0.80, "name": "学术机构"},
        "travel_guide": {"weight": 0.75, "name": "专业导游"},
        "news_media": {"weight": 0.70, "name": "新闻媒体"},
        "travel_site": {"weight": 0.65, "name": "旅游网站"},
        "forum": {"weight": 0.45, "name": "旅游论坛"},
        "social_media": {"weight": 0.35, "name": "社交媒体"},
        "personal_blog": {"weight": 0.25, "name": "个人博客"}
    }

def generate_timestamps(base_time, scenario_type):
    """根据场景类型生成时间戳"""
    base = datetime.fromisoformat(base_time.replace('Z', '+00:00'))
    
    if scenario_type == "time_dominant":
        # 时间主导：新信息 vs 旧信息
        old_time = base - timedelta(days=random.randint(180, 365))
        new_time = base - timedelta(days=random.randint(1, 30))
        return [old_time.isoformat() + 'Z', new_time.isoformat() + 'Z']
    
    elif scenario_type == "weight_dominant":
        # 权重主导：时间相近，权重差异大
        time1 = base - timedelta(days=random.randint(1, 60))
        time2 = base - timedelta(days=random.randint(1, 60))
        return [time1.isoformat() + 'Z', time2.isoformat() + 'Z']
    
    elif scenario_type == "complex_tradeoff":
        # 复杂权衡：权威但旧 vs 新但不权威
        old_time = base - timedelta(days=random.randint(120, 300))
        new_time = base - timedelta(days=random.randint(1, 30))
        return [old_time.isoformat() + 'Z', new_time.isoformat() + 'Z']
    
    else:  # edge_case
        # 边界情况：时间和权重都相近
        time1 = base - timedelta(days=random.randint(30, 90))
        time2 = base - timedelta(days=random.randint(30, 90))
        return [time1.isoformat() + 'Z', time2.isoformat() + 'Z']

def create_weight_dominant_conflict(node, sources, difficulty="medium"):
    """创建权重主导型冲突"""

    if difficulty == "easy":
        # 简单：权重差异极大
        high_weight_source = random.choice(["government", "tourism_bureau"])
        low_weight_source = random.choice(["social_media", "personal_blog"])
        content_quality_diff = "极大"
    elif difficulty == "medium":
        # 中等：权重差异明显
        high_weight_source = random.choice(["tourism_bureau", "official_site", "academic"])
        low_weight_source = random.choice(["forum", "social_media"])
        content_quality_diff = "明显"
    else:  # hard
        # 困难：权重差异较小，需要仔细判断
        high_weight_source = random.choice(["official_site", "academic", "travel_guide"])
        low_weight_source = random.choice(["travel_site", "news_media"])
        content_quality_diff = "较小"

    timestamps = generate_timestamps(node['pub_timestamp'], "weight_dominant")

    # 根据难度创建不同质量的内容
    original_desc = node.get('description', node['name'])

    if difficulty == "easy":
        high_quality_content = f"{original_desc}（官方权威认证，详细介绍包含历史文化背景）"
        low_quality_content = f"听说{node['name']}还行吧，没去过"
    elif difficulty == "medium":
        high_quality_content = f"{original_desc}（经过专业验证的详细信息）"
        low_quality_content = f"{node['name']}据网友反馈还不错，值得一去"
    else:  # hard
        high_quality_content = f"{original_desc}（来源可靠，信息相对完整）"
        low_quality_content = f"{node['name']}根据最近的游客评价，体验还可以"

    conflicts = [
        {
            "entity_name": node['name'],
            "attribute": "description",
            "value": high_quality_content,
            "source": {
                "type": high_weight_source,
                "url": f"http://{high_weight_source}.gov/{node['name'].replace(' ', '_')}",
                "weight": sources[high_weight_source]["weight"]
            },
            "timestamp": timestamps[0]
        },
        {
            "entity_name": node['name'],
            "attribute": "description",
            "value": low_quality_content,
            "source": {
                "type": low_weight_source,
                "url": f"http://{low_weight_source}.com/post{random.randint(100,999)}",
                "weight": sources[low_weight_source]["weight"]
            },
            "timestamp": timestamps[1]
        }
    ]

    return {
        "name": f"权重主导({difficulty}) - {node['name']}描述冲突",
        "conflict_type": "weight_dominant",
        "difficulty": difficulty,
        "conflicts": conflicts,
        "expected_winner_id": conflicts[0]["source"]["url"],  # 高权重源应该获胜
        "explanation": f"权威性差异{content_quality_diff}，应选择权重更高的{sources[high_weight_source]['name']}"
    }

def create_time_dominant_conflict(node, sources, difficulty="medium"):
    """创建时间主导型冲突"""

    if difficulty == "easy":
        # 简单：时间差异很大，权重完全相同
        source1 = source2 = random.choice(["travel_guide", "news_media"])
        time_gap_days = random.randint(300, 500)  # 时间差很大
    elif difficulty == "medium":
        # 中等：时间差异明显，权重略有不同
        source1 = random.choice(["travel_guide", "news_media", "travel_site"])
        source2 = random.choice(["travel_guide", "news_media", "travel_site"])
        time_gap_days = random.randint(120, 300)  # 时间差中等
    else:  # hard
        # 困难：时间差异较小，权重有一定差异
        source1 = random.choice(["academic", "official_site"])
        source2 = random.choice(["travel_site", "news_media"])
        time_gap_days = random.randint(30, 120)   # 时间差较小

    # 生成特定时间差的时间戳
    base_time = datetime.fromisoformat(node['pub_timestamp'].replace('Z', '+00:00'))
    old_time = base_time - timedelta(days=time_gap_days)
    new_time = base_time - timedelta(days=random.randint(1, 30))

    timestamps = [old_time.isoformat() + 'Z', new_time.isoformat() + 'Z']

    # 根据难度创建不同的信息内容
    if difficulty == "easy":
        old_content = f"2022年数据显示，{node['name']}游客满意度为85%"
        new_content = f"2024年最新调查：{node['name']}游客满意度提升至92%"
    elif difficulty == "medium":
        old_content = f"去年统计，{node['name']}年接待游客约{random.randint(50,200)}万人次"
        new_content = f"最新数据显示，{node['name']}今年游客量增长{random.randint(10,30)}%"
    else:  # hard
        old_content = f"据{sources[source1]['name']}报告，{node['name']}综合评分{random.randint(80,90)}/100"
        new_content = f"根据{sources[source2]['name']}最新评估，{node['name']}评分为{random.randint(85,95)}/100"

    conflicts = [
        {
            "entity_name": node['name'],
            "attribute": "visitor_stats",
            "value": old_content,
            "source": {
                "type": source1,
                "url": f"http://{source1}.com/report-{old_time.year}",
                "weight": sources[source1]["weight"]
            },
            "timestamp": timestamps[0]  # 较旧的时间
        },
        {
            "entity_name": node['name'],
            "attribute": "visitor_stats",
            "value": new_content,
            "source": {
                "type": source2,
                "url": f"http://{source2}.com/latest-{new_time.year}",
                "weight": sources[source2]["weight"]
            },
            "timestamp": timestamps[1]  # 较新的时间
        }
    ]

    return {
        "name": f"时间主导({difficulty}) - {node['name']}数据更新",
        "conflict_type": "time_dominant",
        "difficulty": difficulty,
        "conflicts": conflicts,
        "expected_winner_id": conflicts[1]["source"]["url"],  # 新信息应该获胜
        "explanation": f"时间差异{time_gap_days}天，应选择更新的信息"
    }

def create_complex_tradeoff_conflict(node, sources, difficulty="hard"):
    """创建复杂权衡型冲突"""

    if difficulty == "easy":
        # 简单：权威性明显更重要（权威但稍旧 vs 很新但很不权威）
        high_weight_source = random.choice(["government", "tourism_bureau"])
        low_weight_source = random.choice(["personal_blog", "social_media"])
        time_gap = random.randint(60, 120)  # 权威信息稍旧
        expected_winner = 0  # 权威源获胜
    elif difficulty == "medium":
        # 中等：需要平衡考虑（权威但较旧 vs 较新但中等权威）
        high_weight_source = random.choice(["tourism_bureau", "official_site"])
        low_weight_source = random.choice(["travel_site", "news_media"])
        time_gap = random.randint(120, 240)  # 权威信息较旧
        expected_winner = 0 if random.random() > 0.3 else 1  # 70%权威源获胜
    else:  # hard
        # 困难：真正的权衡（权威但很旧 vs 很新但权威性一般）
        high_weight_source = random.choice(["academic", "official_site"])
        low_weight_source = random.choice(["travel_guide", "news_media"])
        time_gap = random.randint(240, 400)  # 权威信息很旧
        expected_winner = 1 if time_gap > 300 else 0  # 很旧时新信息获胜

    # 生成时间戳
    base_time = datetime.fromisoformat(node['pub_timestamp'].replace('Z', '+00:00'))
    old_time = base_time - timedelta(days=time_gap)
    new_time = base_time - timedelta(days=random.randint(1, 30))

    timestamps = [old_time.isoformat() + 'Z', new_time.isoformat() + 'Z']

    # 根据难度创建内容
    base_rating = random.randint(75, 95)

    if difficulty == "easy":
        authoritative_content = f"官方权威评估：{node['name']}综合评分{base_rating}/100，基于详细调研"
        new_content = f"网友爆料：{node['name']}感觉还行，大概{base_rating + random.randint(-15, 15)}分吧"
    elif difficulty == "medium":
        authoritative_content = f"专业机构评估：{node['name']}评分{base_rating}/100，数据来源可靠"
        new_content = f"最新游客反馈：{node['name']}体验评分约{base_rating + random.randint(-8, 12)}/100"
    else:  # hard
        authoritative_content = f"学术研究报告：{node['name']}综合指数{base_rating}/100"
        new_content = f"近期调查显示：{node['name']}游客满意度{base_rating + random.randint(-5, 10)}/100"

    conflicts = [
        {
            "entity_name": node['name'],
            "attribute": "evaluation_score",
            "value": authoritative_content,
            "source": {
                "type": high_weight_source,
                "url": f"http://{high_weight_source}.org/evaluation-{old_time.year}",
                "weight": sources[high_weight_source]["weight"]
            },
            "timestamp": timestamps[0]  # 较旧但权威
        },
        {
            "entity_name": node['name'],
            "attribute": "evaluation_score",
            "value": new_content,
            "source": {
                "type": low_weight_source,
                "url": f"http://{low_weight_source}.com/survey-{new_time.year}",
                "weight": sources[low_weight_source]["weight"]
            },
            "timestamp": timestamps[1]  # 较新但不太权威
        }
    ]

    return {
        "name": f"复杂权衡({difficulty}) - {node['name']}评分争议",
        "conflict_type": "complex_tradeoff",
        "difficulty": difficulty,
        "conflicts": conflicts,
        "expected_winner_id": conflicts[expected_winner]["source"]["url"],
        "explanation": f"权威性vs时效性权衡，时间差{time_gap}天，{'权威性' if expected_winner == 0 else '时效性'}更重要"
    }

def create_edge_case_conflict(node, sources, difficulty="hard"):
    """创建边界情况冲突"""

    if difficulty == "easy":
        # 简单：权重时间都相近，但内容质量差异明显
        source1 = random.choice(["travel_guide", "news_media"])
        source2 = random.choice(["travel_guide", "news_media"])
        content_quality_diff = "明显"
    elif difficulty == "medium":
        # 中等：权重时间相近，内容质量有一定差异
        source1 = random.choice(["travel_guide", "news_media", "travel_site"])
        source2 = random.choice(["travel_site", "academic"])
        content_quality_diff = "一定"
    else:  # hard
        # 困难：权重时间内容都很相近，需要细微判断
        source1 = random.choice(["travel_site", "news_media"])
        source2 = random.choice(["travel_guide", "academic"])
        content_quality_diff = "微小"

    # 生成相近的时间戳
    base_time = datetime.fromisoformat(node['pub_timestamp'].replace('Z', '+00:00'))
    time1 = base_time - timedelta(days=random.randint(30, 90))
    time2 = base_time - timedelta(days=random.randint(30, 90))

    timestamps = [time1.isoformat() + 'Z', time2.isoformat() + 'Z']

    # 根据难度创建不同质量的内容
    location = node.get('location', '拉萨')

    if difficulty == "easy":
        content1 = f"{location}市区"
        content2 = f"{location}市{node['name']}具体位置：详细地址包含街道门牌号信息"
        expected_winner = 1  # 详细内容获胜
    elif difficulty == "medium":
        content1 = f"{location}市{node['name']}附近区域"
        content2 = f"{location}市{node['name']}所在街道，交通便利"
        expected_winner = 1 if len(content2) > len(content1) else 0
    else:  # hard
        content1 = f"{location}市{node['name']}位置，靠近市中心"
        content2 = f"{location}市{node['name']}地址，交通方便"
        # 困难情况下，选择权重稍高的源
        expected_winner = 0 if sources[source1]["weight"] >= sources[source2]["weight"] else 1

    conflicts = [
        {
            "entity_name": node['name'],
            "attribute": "location_detail",
            "value": content1,
            "source": {
                "type": source1,
                "url": f"http://{source1}.com/location-{random.randint(100,999)}",
                "weight": sources[source1]["weight"]
            },
            "timestamp": timestamps[0]
        },
        {
            "entity_name": node['name'],
            "attribute": "location_detail",
            "value": content2,
            "source": {
                "type": source2,
                "url": f"http://{source2}.com/address-{random.randint(100,999)}",
                "weight": sources[source2]["weight"]
            },
            "timestamp": timestamps[1]
        }
    ]

    return {
        "name": f"边界情况({difficulty}) - {node['name']}位置信息",
        "conflict_type": "edge_case",
        "difficulty": difficulty,
        "conflicts": conflicts,
        "expected_winner_id": conflicts[expected_winner]["source"]["url"],
        "explanation": f"权重时间相近，内容质量差异{content_quality_diff}，选择{'更详细' if expected_winner == 1 else '权重稍高'}的信息"
    }

def generate_test_dataset(nodes, num_cases=100):
    """生成测试数据集"""
    sources = create_source_types()
    test_cases = []

    # 精心设计的冲突类型分布 - 基于实际应用场景的重要性
    conflict_distribution = {
        "weight_dominant": 30,    # 30% - 权威性冲突最常见
        "time_dominant": 25,      # 25% - 时效性冲突很重要
        "complex_tradeoff": 25,   # 25% - 复杂权衡是核心挑战
        "edge_case": 20          # 20% - 边界情况需要特别处理
    }

    # 难度分布设计 - 模拟真实世界的复杂度
    difficulty_distribution = {
        "easy": 20,      # 20% - 明显的冲突
        "medium": 35,    # 35% - 中等复杂度
        "hard": 45       # 45% - 困难案例占主要部分
    }

    case_id = 1
    used_nodes = set()  # 避免重复使用同一个景点

    # 按照分布生成测试案例
    for conflict_type, count in conflict_distribution.items():
        for i in range(count):
            # 选择未使用的景点，如果用完了就重新开始
            available_nodes = [n for n in nodes if n['name'] not in used_nodes]
            if not available_nodes:
                used_nodes.clear()
                available_nodes = nodes

            node = random.choice(available_nodes)
            used_nodes.add(node['name'])

            # 根据冲突类型和进度确定难度
            if i < count * 0.2:
                target_difficulty = "easy"
            elif i < count * 0.55:
                target_difficulty = "medium"
            else:
                target_difficulty = "hard"

            # 根据冲突类型创建测试案例
            if conflict_type == "weight_dominant":
                test_case = create_weight_dominant_conflict(node, sources, target_difficulty)
            elif conflict_type == "time_dominant":
                test_case = create_time_dominant_conflict(node, sources, target_difficulty)
            elif conflict_type == "complex_tradeoff":
                test_case = create_complex_tradeoff_conflict(node, sources, target_difficulty)
            else:  # edge_case
                test_case = create_edge_case_conflict(node, sources, target_difficulty)

            test_case["id"] = case_id
            test_case["difficulty"] = target_difficulty
            test_cases.append(test_case)
            case_id += 1

    # 随机打乱顺序，避免按类型聚集
    random.shuffle(test_cases)

    return test_cases

def main():
    """主函数"""
    print("🚀 基于拉萨知识图谱生成冲突解决策略测试集")
    print("=" * 60)
    
    # 加载数据
    nodes = load_lhasa_data()
    if not nodes:
        print("❌ 无法加载数据，程序退出")
        return
    
    print(f"✅ 成功加载 {len(nodes)} 个景点数据")
    
    # 生成测试集
    num_cases = 100  # 生成100个测试案例
    test_cases = generate_test_dataset(nodes, num_cases)
    
    print(f"✅ 成功生成 {len(test_cases)} 个测试案例")
    
    # 统计信息
    type_counts = {}
    difficulty_counts = {}
    
    for case in test_cases:
        conflict_type = case["conflict_type"]
        difficulty = case["difficulty"]
        
        type_counts[conflict_type] = type_counts.get(conflict_type, 0) + 1
        difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
    
    print(f"\n📊 测试集统计:")
    print(f"   冲突类型分布: {type_counts}")
    print(f"   难度分布: {difficulty_counts}")
    
    # 保存测试集
    output_file = "lhasa_conflict_test_dataset.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试集已保存到: {output_file}")
    
    # 显示几个示例
    print(f"\n📋 测试案例示例:")
    for i, case in enumerate(test_cases[:3]):
        print(f"\n案例 {i+1}: {case['name']}")
        print(f"   类型: {case['conflict_type']}")
        print(f"   难度: {case['difficulty']}")
        print(f"   冲突数: {len(case['conflicts'])}")

if __name__ == "__main__":
    main()
