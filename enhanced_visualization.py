#!/usr/bin/env python3
"""
增强对比度的实验结果可视化

提供多种第四个图的替代方案，增强整体对比度
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体和高对比度样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')  # 使用默认样式以获得更好的对比度

def create_high_contrast_performance_chart():
    """创建高对比度性能对比图"""
    
    # 实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景下的表现
    weight_dominant = [1.000, 0.500, 1.000]
    time_dominant = [0.160, 1.000, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.800]
    edge_case = [0.650, 0.350, 0.900]
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000]
    medium_performance = [0.722, 0.472, 0.917]
    hard_performance = [0.591, 0.705, 0.682]
    
    # 高对比度颜色方案
    colors_high_contrast = ['#D32F2F', '#1976D2', '#388E3C']  # 深红、深蓝、深绿
    
    # 创建综合图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=18, fontweight='bold', y=0.98)
    
    # 1. 整体性能对比 - 增强对比度
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors_high_contrast, 
                    alpha=0.9, edgecolor='black', linewidth=2)
    ax1.set_title('整体准确率对比', fontsize=16, fontweight='bold')
    ax1.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax1.set_ylim(0, 1.0)
    ax1.grid(True, alpha=0.3, linestyle='--')
    
    # 添加数值标签 - 更大更粗
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold', color='black')
    
    # 2. 场景性能对比 - 分组柱状图
    x = np.arange(len(strategies))
    width = 0.2
    
    bars2_1 = ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', 
                      color='#D32F2F', alpha=0.9, edgecolor='black', linewidth=1)
    bars2_2 = ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', 
                      color='#1976D2', alpha=0.9, edgecolor='black', linewidth=1)
    bars2_3 = ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', 
                      color='#388E3C', alpha=0.9, edgecolor='black', linewidth=1)
    bars2_4 = ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', 
                      color='#F57C00', alpha=0.9, edgecolor='black', linewidth=1)
    
    ax2.set_title('各场景类型性能对比', fontsize=16, fontweight='bold', pad=15)
    ax2.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax2.set_xlabel('策略', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, fontweight='bold')
    ax2.legend(fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3, linestyle='--')
    ax2.set_ylim(0, 1.1)
    
    # 3. 难度性能对比 - 热力图风格
    difficulty_data = np.array([easy_performance, medium_performance, hard_performance])
    difficulty_labels = ['简单', '中等', '困难']
    
    im = ax3.imshow(difficulty_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    ax3.set_title('各难度等级性能热力图', fontsize=16, fontweight='bold', pad=15)
    ax3.set_xticks(range(len(strategies)))
    ax3.set_xticklabels(strategies, fontweight='bold')
    ax3.set_yticks(range(len(difficulty_labels)))
    ax3.set_yticklabels(difficulty_labels, fontweight='bold')
    
    # 添加数值标签
    for i in range(len(difficulty_labels)):
        for j in range(len(strategies)):
            text = ax3.text(j, i, f'{difficulty_data[i, j]:.2f}',
                           ha="center", va="center", color="black", 
                           fontsize=12, fontweight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax3, shrink=0.8)
    cbar.set_label('准确率', fontsize=12, fontweight='bold')
    
    return fig, ax4  # 返回第四个子图的轴对象

def create_alternative_fourth_charts(ax4):
    """创建第四个图的多种替代方案"""
    
    # 方案1：策略优势雷达图
    create_strategy_advantage_radar(ax4)

def create_strategy_advantage_radar(ax):
    """方案1：策略优势雷达图"""
    
    # 各策略的优势维度评分
    categories = ['权威性\n识别', '时效性\n识别', '复杂\n权衡', '边界\n处理', '整体\n稳定性', '计算\n效率']
    
    # 评分数据 (0-1)
    weight_first_scores = [1.0, 0.2, 0.6, 0.6, 0.7, 1.0]
    latest_first_scores = [0.3, 1.0, 0.4, 0.4, 0.6, 1.0]
    adaptive_scores = [1.0, 0.8, 0.9, 0.9, 0.8, 0.7]
    
    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    # 闭合数据
    weight_first_scores += weight_first_scores[:1]
    latest_first_scores += latest_first_scores[:1]
    adaptive_scores += adaptive_scores[:1]
    
    # 清除原有内容并设置为极坐标
    ax.clear()
    ax = plt.subplot(2, 2, 4, projection='polar')
    
    # 绘制三条线 - 高对比度
    ax.plot(angles, weight_first_scores, 'o-', linewidth=3, label='Weight-First', 
            color='#D32F2F', markersize=8)
    ax.fill(angles, weight_first_scores, alpha=0.2, color='#D32F2F')
    
    ax.plot(angles, latest_first_scores, 's-', linewidth=3, label='Latest-First', 
            color='#1976D2', markersize=8)
    ax.fill(angles, latest_first_scores, alpha=0.2, color='#1976D2')
    
    ax.plot(angles, adaptive_scores, '^-', linewidth=4, label='自适应Hybrid', 
            color='#388E3C', markersize=10)
    ax.fill(angles, adaptive_scores, alpha=0.3, color='#388E3C')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=12, fontweight='bold')
    ax.set_ylim(0, 1)
    ax.set_title('策略能力雷达图', fontsize=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12, fontweight='bold')
    ax.grid(True, alpha=0.3)

def create_performance_improvement_chart(ax):
    """方案2：性能提升对比图"""
    
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    baseline = 0.630  # Weight-First作为基准
    improvements = [0, 0.560 - baseline, 0.830 - baseline]
    
    # 颜色：基准为灰色，下降为红色，提升为绿色
    colors = ['#757575', '#D32F2F', '#388E3C']
    
    bars = ax.bar(strategies, improvements, color=colors, alpha=0.9, 
                  edgecolor='black', linewidth=2)
    ax.set_title('相比Weight-First的性能变化', fontsize=16, fontweight='bold', pad=15)
    ax.set_ylabel('准确率变化', fontsize=14, fontweight='bold')
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=2)
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 添加数值标签
    for bar, improvement in zip(bars, improvements):
        height = bar.get_height()
        if height >= 0:
            label = f'+{improvement:.1%}' if improvement > 0 else f'{improvement:.1%}'
            y_pos = height + 0.01
            va = 'bottom'
        else:
            label = f'{improvement:.1%}'
            y_pos = height - 0.01
            va = 'top'
        
        ax.text(bar.get_x() + bar.get_width()/2., y_pos, label,
                ha='center', va=va, fontsize=14, fontweight='bold', color='black')

def create_scenario_performance_matrix(ax):
    """方案3：场景性能矩阵图"""
    
    # 性能数据矩阵
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    performance_matrix = np.array([
        [1.000, 0.500, 1.000],  # 权重主导
        [0.160, 1.000, 0.600],  # 时间主导
        [0.640, 0.360, 0.800],  # 复杂权衡
        [0.650, 0.350, 0.900]   # 边界情况
    ])
    
    # 创建热力图
    im = ax.imshow(performance_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    ax.set_title('策略-场景性能矩阵', fontsize=16, fontweight='bold', pad=15)
    ax.set_xticks(range(len(strategies)))
    ax.set_xticklabels(strategies, fontweight='bold', fontsize=12)
    ax.set_yticks(range(len(scenarios)))
    ax.set_yticklabels(scenarios, fontweight='bold', fontsize=12)
    
    # 添加数值标签
    for i in range(len(scenarios)):
        for j in range(len(strategies)):
            value = performance_matrix[i, j]
            color = 'white' if value < 0.5 else 'black'
            text = ax.text(j, i, f'{value:.2f}', ha="center", va="center", 
                          color=color, fontsize=12, fontweight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('准确率', fontsize=12, fontweight='bold')

def create_comprehensive_score_chart(ax):
    """方案4：综合评分对比"""
    
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 各维度评分 (0-100)
    accuracy_scores = [63.0, 56.0, 83.0]
    consistency_scores = [85.0, 75.0, 90.0]  # 一致性评分
    adaptability_scores = [20.0, 30.0, 95.0]  # 适应性评分
    efficiency_scores = [95.0, 98.0, 80.0]   # 效率评分
    
    x = np.arange(len(strategies))
    width = 0.2
    
    bars1 = ax.bar(x - 1.5*width, accuracy_scores, width, label='准确率', 
                   color='#D32F2F', alpha=0.9, edgecolor='black')
    bars2 = ax.bar(x - 0.5*width, consistency_scores, width, label='一致性', 
                   color='#1976D2', alpha=0.9, edgecolor='black')
    bars3 = ax.bar(x + 0.5*width, adaptability_scores, width, label='适应性', 
                   color='#388E3C', alpha=0.9, edgecolor='black')
    bars4 = ax.bar(x + 1.5*width, efficiency_scores, width, label='效率', 
                   color='#F57C00', alpha=0.9, edgecolor='black')
    
    ax.set_title('综合性能评分对比', fontsize=16, fontweight='bold', pad=15)
    ax.set_ylabel('评分', fontsize=14, fontweight='bold')
    ax.set_xlabel('策略', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(strategies, fontweight='bold')
    ax.legend(fontsize=12, fontweight='bold')
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.set_ylim(0, 100)

def main():
    """主函数 - 生成多种可视化方案"""
    
    print("🎨 创建增强对比度的实验结果可视化")
    print("=" * 60)
    
    # 方案1：策略优势雷达图
    print("📊 创建方案1：策略优势雷达图...")
    fig1, ax4_1 = create_high_contrast_performance_chart()
    create_strategy_advantage_radar(ax4_1)
    plt.savefig('enhanced_results_radar.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    # 方案2：性能提升对比图
    print("📊 创建方案2：性能提升对比图...")
    fig2, ax4_2 = create_high_contrast_performance_chart()
    create_performance_improvement_chart(ax4_2)
    plt.savefig('enhanced_results_improvement.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    # 方案3：场景性能矩阵图
    print("📊 创建方案3：场景性能矩阵图...")
    fig3, ax4_3 = create_high_contrast_performance_chart()
    create_scenario_performance_matrix(ax4_3)
    plt.savefig('enhanced_results_matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    # 方案4：综合评分对比
    print("📊 创建方案4：综合评分对比...")
    fig4, ax4_4 = create_high_contrast_performance_chart()
    create_comprehensive_score_chart(ax4_4)
    plt.savefig('enhanced_results_comprehensive.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print(f"\n🎉 增强对比度可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • enhanced_results_radar.png - 策略优势雷达图")
    print(f"   • enhanced_results_improvement.png - 性能提升对比图")
    print(f"   • enhanced_results_matrix.png - 场景性能矩阵图")
    print(f"   • enhanced_results_comprehensive.png - 综合评分对比")
    print(f"\n💡 建议:")
    print(f"   - 雷达图：展示各策略的能力特征")
    print(f"   - 提升图：突出自适应策略的优势")
    print(f"   - 矩阵图：清晰显示策略-场景匹配")
    print(f"   - 综合图：多维度全面评估")

if __name__ == "__main__":
    main()
