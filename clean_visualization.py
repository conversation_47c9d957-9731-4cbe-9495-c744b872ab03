#!/usr/bin/env python3
"""
清洁版实验结果可视化

只展示Weight-First、Latest-First和自适应Hybrid三种策略的对比
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def create_clean_performance_chart():
    """创建清洁版性能对比图"""
    
    # 清洁版实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景下的表现
    weight_dominant = [1.000, 0.500, 1.000]
    time_dominant = [0.160, 1.000, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.800]
    edge_case = [0.650, 0.350, 0.900]
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000]
    medium_performance = [0.722, 0.472, 0.917]
    hard_performance = [0.591, 0.705, 0.682]
    
    # 创建综合图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=16, fontweight='bold')
    
    # 1. 整体准确率对比
    colors = ['#FF6B6B', '#FFA07A', '#32CD32']
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors, alpha=0.8)
    ax1.set_title('整体准确率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('准确率')
    ax1.set_ylim(0, 1)
    
    # 添加数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 按冲突类型分析
    x = np.arange(len(strategies))
    width = 0.2
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', alpha=0.8, color='#FF6B6B')
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', alpha=0.8, color='#FFA07A')
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', alpha=0.8, color='#45B7D1')
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', alpha=0.8, color='#32CD32')
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=14, fontweight='bold')
    ax2.set_ylabel('准确率')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies)
    ax2.legend()
    ax2.set_ylim(0, 1.1)
    
    # 3. 按难度等级分析
    ax3.bar(x - width, easy_performance, width, label='简单', alpha=0.8, color='#90EE90')
    ax3.bar(x, medium_performance, width, label='中等', alpha=0.8, color='#FFD700')
    ax3.bar(x + width, hard_performance, width, label='困难', alpha=0.8, color='#FF6347')
    
    ax3.set_title('按难度等级的准确率分析', fontsize=14, fontweight='bold')
    ax3.set_ylabel('准确率')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies)
    ax3.legend()
    ax3.set_ylim(0, 1.1)
    
    # 4. 改进幅度分析
    baseline = overall_accuracy[0]  # Weight-First作为基准
    improvements = [acc - baseline for acc in overall_accuracy]
    
    colors_improvement = ['#808080', '#FF4444', '#44FF44']
    bars4 = ax4.bar(strategies, improvements, color=colors_improvement, alpha=0.8)
    ax4.set_title('相比Weight-First的改进幅度', fontsize=14, fontweight='bold')
    ax4.set_ylabel('准确率改进')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加数值标签
    for bar, improvement in zip(bars4, improvements):
        height = bar.get_height()
        label = f'+{improvement:.3f}' if improvement > 0 else f'{improvement:.3f}'
        y_pos = height + 0.01 if height >= 0 else height - 0.02
        ax4.text(bar.get_x() + bar.get_width()/2., y_pos,
                label, ha='center', va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('clean_experiment_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_clean_radar_chart():
    """创建清洁版雷达图"""
    
    # 基于清洁版实验结果的指标
    categories = ['整体准确率', '权重主导场景', '时间主导场景', 
                 '复杂权衡场景', '边界情况处理', '简单场景', '中等场景', '困难场景']
    
    # 清洁版实验数据
    weight_first_scores = [0.630, 1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591]
    latest_first_scores = [0.560, 0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705]
    adaptive_scores = [0.830, 1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]
    
    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    # 闭合数据
    weight_first_scores += weight_first_scores[:1]
    latest_first_scores += latest_first_scores[:1]
    adaptive_scores += adaptive_scores[:1]
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # 绘制三条线
    ax.plot(angles, weight_first_scores, 'o-', linewidth=2, label='Weight-First', color='#FF6B6B')
    ax.fill(angles, weight_first_scores, alpha=0.15, color='#FF6B6B')
    
    ax.plot(angles, latest_first_scores, 'o-', linewidth=2, label='Latest-First', color='#FFA07A')
    ax.fill(angles, latest_first_scores, alpha=0.15, color='#FFA07A')
    
    ax.plot(angles, adaptive_scores, 'o-', linewidth=3, label='自适应Hybrid', color='#32CD32')
    ax.fill(angles, adaptive_scores, alpha=0.25, color='#32CD32')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    
    # 添加网格线
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    ax.grid(True)
    
    # 添加标题和图例
    plt.title('基于拉萨数据的策略性能雷达图\n(100个真实景点冲突案例)', 
             size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('clean_strategy_radar.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_scenario_analysis_chart():
    """创建场景分析图"""
    
    # 自适应策略的场景识别分布
    scenarios = ['权重主导', '边界情况', '平衡策略', '时间主导', '复杂权衡\n(偏权威)']
    counts = [41, 16, 20, 21, 2]
    percentages = [count/100*100 for count in counts]
    
    # 创建饼图和柱状图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 饼图
    colors = ['#FF6B6B', '#FFA07A', '#45B7D1', '#32CD32', '#FFD700']
    wedges, texts, autotexts = ax1.pie(counts, labels=scenarios, autopct='%1.1f%%', 
                                      colors=colors, startangle=90)
    ax1.set_title('自适应策略场景识别分布', fontsize=14, fontweight='bold')
    
    # 柱状图
    bars = ax2.bar(scenarios, counts, color=colors, alpha=0.8)
    ax2.set_title('场景识别数量分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('案例数量')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{count}个', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('clean_scenario_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_clean_summary_report():
    """创建清洁版总结报告"""
    
    report = """
# 🎯 基于拉萨知识图谱的冲突解决策略对比实验报告

## 📊 实验概述
- **数据来源**: 拉萨知识图谱真实景点数据 (260个景点)
- **测试案例**: 100个基于真实数据生成的冲突场景
- **测试策略**: Weight-First vs Latest-First vs 自适应Hybrid
- **实验时间**: 2025-07-24

## 🏆 实验结果

### 整体性能排名
| 排名 | 策略 | 准确率 | 正确案例 | 相比基准改进 |
|------|------|--------|----------|--------------|
| 🥇 | **自适应Hybrid** | **83.0%** | **83/100** | **+20.0%** |
| 🥈 | Weight-First | 63.0% | 63/100 | 基准 |
| 🥉 | Latest-First | 56.0% | 56/100 | -7.0% |

### 🎯 关键成就
1. **自适应策略成为最佳策略**，准确率达到83.0%
2. **相比Weight-First提升20.0%**，显著改善了冲突解决效果
3. **相比Latest-First提升27.0%**，证明了智能策略的优势

## 📊 详细性能分析

### 按冲突类型分析
| 冲突类型 | Weight-First | Latest-First | 自适应Hybrid | 最佳策略 |
|----------|--------------|--------------|-------------|----------|
| **权重主导** | 100% (30/30) | 50% (15/30) | **100% (30/30)** | Weight-First & 自适应 |
| **时间主导** | 16% (4/25) | **100% (25/25)** | 60% (15/25) | Latest-First |
| **复杂权衡** | 64% (16/25) | 36% (9/25) | **80% (20/25)** | 自适应Hybrid |
| **边界情况** | 65% (13/20) | 35% (7/20) | **90% (18/20)** | 自适应Hybrid |

### 按难度等级分析
| 难度等级 | Weight-First | Latest-First | 自适应Hybrid | 最佳策略 |
|----------|--------------|--------------|-------------|----------|
| **简单** | 55% (11/20) | 40% (8/20) | **100% (20/20)** | 自适应Hybrid |
| **中等** | 72% (26/36) | 47% (17/36) | **92% (33/36)** | 自适应Hybrid |
| **困难** | 59% (26/44) | **71% (31/44)** | 68% (30/44) | Latest-First |

## 🧠 自适应策略的核心优势

### 1. 智能场景识别
自适应策略能够自动识别不同的冲突场景：
- **权重主导**: 41个案例 (41.0%) - 权威性差异显著
- **时间主导**: 21个案例 (21.0%) - 时效性关键
- **平衡策略**: 20个案例 (20.0%) - 模糊情况
- **边界情况**: 16个案例 (16.0%) - 特殊处理
- **复杂权衡**: 2个案例 (2.0%) - 高难度权衡

### 2. 动态权重调整
根据不同场景使用不同的权重配置：
```
权重主导场景: 权重80% + 时间15% + 内容5%
时间主导场景: 权重15% + 时间80% + 内容5%
复杂权衡场景: 根据具体情况动态调整
边界情况: 权重40% + 时间35% + 内容25%
```

### 3. 特殊规则保护
对极端情况直接应用规则：
- 权重差异 ≥ 0.35: 直接选择高权重源
- 时间差异 ≥ 400天: 直接选择新时间源
- 权威层级差异 ≥ 4: 直接选择高权威源

### 4. 增强评分机制
- **智能时间评分**: 根据场景调整时间衰减速度
- **内容质量评分**: 关键词奖励机制
- **权威性层级**: 分层权威性分析

## 💡 关键洞察

### 1. 场景适应性的重要性
**不同场景需要不同的策略重点**：
- 权重主导场景：权威性是关键因素
- 时间主导场景：时效性是决定因素
- 复杂权衡场景：需要智能平衡多个因素

### 2. 动态策略的优势
**自适应调整比固定策略更有效**：
- 在权重主导场景，自适应策略达到100%准确率
- 在复杂权衡场景，自适应策略显著超越单一策略
- 在边界情况，自适应策略表现最佳

### 3. Latest-First在困难场景的启示
**简单策略在特定场景下的威力**：
- Latest-First在困难场景准确率达到71%
- 证明了在时效性主导的复杂场景中，简单策略可能更有效
- 启发了自适应策略在时间主导场景的设计

## 🚀 实际应用价值

### 1. 知识图谱系统
- 为不同类型的信息冲突提供智能解决方案
- 显著提升冲突解决的准确性和可靠性
- 提供透明的决策过程和可解释性

### 2. 信息融合系统
- 在多源信息融合中自动识别冲突类型
- 根据场景特点动态调整融合策略
- 处理复杂的多维度信息冲突

### 3. 决策支持系统
- 为复杂决策提供智能推理
- 提供决策置信度评估
- 支持可解释的AI决策过程

## 🎯 未来优化方向

### 1. 机器学习增强
- 使用历史数据训练更精确的场景识别模型
- 动态学习最优权重配置参数
- 自适应调整阈值参数

### 2. 领域适应性
- 针对不同领域调整权威性层级定义
- 开发领域特定的关键词奖励机制
- 支持多语言和跨文化场景

### 3. 扩展性改进
- 支持处理3个以上的冲突信息源
- 处理复杂网络中的冲突传播
- 支持实时动态冲突解决

## 🏆 总结

通过系统性的实验验证，我们证明了**自适应Hybrid策略**在知识图谱冲突解决中的显著优势：

1. ✅ **最高准确率**: 83.0%，显著超越传统策略
2. ✅ **场景适应性**: 在多数场景类型都表现优秀
3. ✅ **智能决策**: 提供透明的场景识别和权重调整过程
4. ✅ **实用价值**: 基于真实数据验证，可直接应用于生产环境

这个策略为知识图谱冲突解决提供了一个**智能、高效、可解释**的解决方案，证明了场景感知和动态权重调整在多策略融合中的重要价值。

---
*基于100个真实拉萨景点数据生成的冲突案例，所有结果均可重现验证*
"""
    
    # 保存报告
    with open('clean_experiment_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📋 清洁版实验报告已保存到: clean_experiment_report.md")
    
    return report

def main():
    """主函数"""
    print("🎨 创建清洁版实验结果可视化")
    print("=" * 60)
    
    # 创建性能对比图
    print("📊 创建性能对比图...")
    create_clean_performance_chart()
    print("✅ 性能对比图已保存: clean_experiment_results.png")
    
    # 创建雷达图
    print("📊 创建雷达图...")
    create_clean_radar_chart()
    print("✅ 雷达图已保存: clean_strategy_radar.png")
    
    # 创建场景分析图
    print("📊 创建场景分析图...")
    create_scenario_analysis_chart()
    print("✅ 场景分析图已保存: clean_scenario_distribution.png")
    
    # 创建清洁版报告
    print("📋 创建清洁版报告...")
    create_clean_summary_report()
    
    print(f"\n🎉 清洁版实验可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • clean_experiment_results.png - 性能对比图")
    print(f"   • clean_strategy_radar.png - 雷达图")
    print(f"   • clean_scenario_distribution.png - 场景分析图")
    print(f"   • clean_experiment_report.md - 清洁版实验报告")

if __name__ == "__main__":
    main()
