#!/usr/bin/env python3
"""
增强对比度的实验结果可视化

提供多种第四个图的可视化选项，增强整体对比度
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import seaborn as sns

# 设置中文字体和高对比度样式
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False
plt.style.use('default')  # 使用默认样式以获得更好的对比度

def create_high_contrast_performance_chart():
    """创建高对比度性能对比图"""
    
    # 实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景下的表现
    weight_dominant = [1.000, 0.500, 1.000]
    time_dominant = [0.160, 1.000, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.800]
    edge_case = [0.650, 0.350, 0.900]
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000]
    medium_performance = [0.722, 0.472, 0.917]
    hard_performance = [0.591, 0.705, 0.682]
    
    # 创建高对比度图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=18, fontweight='bold', y=0.95)
    
    # 高对比度颜色方案
    colors_main = ['#D32F2F', '#FF8F00', '#2E7D32']  # 深红、橙、深绿
    
    # 1. 整体准确率对比 - 增强对比度
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors_main, alpha=0.9, 
                    edgecolor='black', linewidth=2)
    ax1.set_title('整体准确率对比', fontsize=16, fontweight='bold', pad=15)
    ax1.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加粗体数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', fontweight='bold', fontsize=12)
    
    # 2. 按冲突类型分析 - 增强对比度
    x = np.arange(len(strategies))
    width = 0.18
    
    colors_scenario = ['#B71C1C', '#E65100', '#1565C0', '#2E7D32']  # 深色系
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', 
           color=colors_scenario[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', 
           color=colors_scenario[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', 
           color=colors_scenario[2], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', 
           color=colors_scenario[3], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=16, fontweight='bold', pad=15)
    ax2.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, fontweight='bold')
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.set_ylim(0, 1.1)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 按难度等级分析 - 增强对比度
    colors_difficulty = ['#4CAF50', '#FF9800', '#F44336']  # 绿、橙、红
    
    ax3.bar(x - width, easy_performance, width, label='简单', 
           color=colors_difficulty[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x, medium_performance, width, label='中等', 
           color=colors_difficulty[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x + width, hard_performance, width, label='困难', 
           color=colors_difficulty[2], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax3.set_title('按难度等级的准确率分析', fontsize=16, fontweight='bold', pad=15)
    ax3.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies, fontweight='bold')
    ax3.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax3.set_ylim(0, 1.1)
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 策略优势分析热力图 (新的第四个图)
    create_strategy_heatmap(ax4)
    
    plt.tight_layout()
    plt.savefig('enhanced_contrast_results.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    return fig

def create_strategy_heatmap(ax):
    """创建策略优势分析热力图"""
    
    # 策略在不同场景下的表现数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],  # Weight-First
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],  # Latest-First
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]   # 自适应Hybrid
    ])
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 创建热力图
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold')
    ax.set_yticklabels(strategies, fontweight='bold')
    
    # 添加数值标签
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            text = ax.text(j, i, f'{performance_data[i, j]:.2f}',
                         ha="center", va="center", color="black", fontweight='bold')
    
    ax.set_title('策略在各场景下的表现热力图', fontsize=16, fontweight='bold', pad=15)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('准确率', fontweight='bold')

def create_alternative_fourth_charts():
    """创建多种第四个图的替代方案"""
    
    # 方案1: 策略优势雷达图
    create_strategy_advantage_radar()
    
    # 方案2: 改进幅度瀑布图
    create_improvement_waterfall()
    
    # 方案3: 场景-策略匹配矩阵
    create_scenario_strategy_matrix()
    
    # 方案4: 性能提升趋势图
    create_performance_trend()

def create_strategy_advantage_radar():
    """创建策略优势雷达图"""
    
    categories = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单场景', '中等场景', '困难场景']
    
    weight_first_scores = [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591]
    latest_first_scores = [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705]
    adaptive_scores = [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    weight_first_scores += weight_first_scores[:1]
    latest_first_scores += latest_first_scores[:1]
    adaptive_scores += adaptive_scores[:1]
    
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # 高对比度颜色
    ax.plot(angles, weight_first_scores, 'o-', linewidth=3, label='Weight-First', 
           color='#D32F2F', markersize=8)
    ax.fill(angles, weight_first_scores, alpha=0.2, color='#D32F2F')
    
    ax.plot(angles, latest_first_scores, 's-', linewidth=3, label='Latest-First', 
           color='#FF8F00', markersize=8)
    ax.fill(angles, latest_first_scores, alpha=0.2, color='#FF8F00')
    
    ax.plot(angles, adaptive_scores, '^-', linewidth=4, label='自适应Hybrid', 
           color='#2E7D32', markersize=10)
    ax.fill(angles, adaptive_scores, alpha=0.3, color='#2E7D32')
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontweight='bold')
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontweight='bold')
    ax.grid(True, alpha=0.3)
    
    plt.title('策略性能雷达图对比\n(高对比度版本)', size=16, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    plt.tight_layout()
    plt.savefig('strategy_advantage_radar.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_improvement_waterfall():
    """创建改进幅度瀑布图"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 数据
    categories = ['Weight-First\n(基准)', 'Latest-First\n变化', '自适应Hybrid\n变化']
    values = [0.630, -0.070, 0.200]  # Latest-First相对基准的变化，自适应相对基准的变化
    cumulative = [0.630, 0.560, 0.830]
    
    # 颜色
    colors = ['#2E7D32', '#D32F2F', '#1976D2']
    
    # 绘制瀑布图
    for i, (cat, val, cum) in enumerate(zip(categories, values, cumulative)):
        if i == 0:
            # 基准柱
            ax.bar(i, val, color=colors[i], alpha=0.8, edgecolor='black', linewidth=2)
            ax.text(i, val/2, f'{val:.1%}', ha='center', va='center', 
                   fontweight='bold', fontsize=12, color='white')
        else:
            # 变化柱
            if val > 0:
                ax.bar(i, val, bottom=cumulative[i]-val, color=colors[i], 
                      alpha=0.8, edgecolor='black', linewidth=2)
                ax.text(i, cumulative[i]-val/2, f'+{val:.1%}', ha='center', va='center', 
                       fontweight='bold', fontsize=12, color='white')
            else:
                ax.bar(i, abs(val), bottom=cumulative[i], color=colors[i], 
                      alpha=0.8, edgecolor='black', linewidth=2)
                ax.text(i, cumulative[i]+abs(val)/2, f'{val:.1%}', ha='center', va='center', 
                       fontweight='bold', fontsize=12, color='white')
        
        # 添加累积值标签
        ax.text(i, cumulative[i]+0.03, f'{cumulative[i]:.1%}', ha='center', va='bottom', 
               fontweight='bold', fontsize=14)
    
    ax.set_xticks(range(len(categories)))
    ax.set_xticklabels(categories, fontweight='bold')
    ax.set_ylabel('准确率', fontweight='bold', fontsize=14)
    ax.set_title('策略性能改进瀑布图', fontweight='bold', fontsize=16, pad=20)
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('improvement_waterfall.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_scenario_strategy_matrix():
    """创建场景-策略匹配矩阵"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 数据：每个策略在每个场景下是否为最佳
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 1表示最佳，0.5表示并列最佳，0表示非最佳
    best_strategy_matrix = np.array([
        [0.5, 0, 0, 0, 0, 0, 0],    # Weight-First
        [0, 1, 0, 0, 0, 0, 1],      # Latest-First  
        [0.5, 0, 1, 1, 1, 1, 0]     # 自适应Hybrid
    ])
    
    # 创建热力图
    im = ax.imshow(best_strategy_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold')
    ax.set_yticklabels(strategies, fontweight='bold')
    
    # 添加图标
    symbols = ['', '👑', '🤝']  # 空、最佳、并列
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = best_strategy_matrix[i, j]
            if value == 1:
                symbol = '👑'
            elif value == 0.5:
                symbol = '🤝'
            else:
                symbol = ''
            
            ax.text(j, i, symbol, ha="center", va="center", fontsize=20)
    
    ax.set_title('策略-场景最佳匹配矩阵\n(👑=最佳策略, 🤝=并列最佳)', 
                fontsize=16, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('scenario_strategy_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("🎨 创建增强对比度的实验结果可视化")
    print("=" * 60)
    
    # 创建高对比度主图
    print("📊 创建高对比度性能对比图...")
    create_high_contrast_performance_chart()
    print("✅ 高对比度图已保存: enhanced_contrast_results.png")
    
    # 创建替代的第四个图选项
    print("📊 创建第四个图的替代方案...")
    
    print("   • 策略优势雷达图...")
    create_strategy_advantage_radar()
    print("   ✅ 已保存: strategy_advantage_radar.png")
    
    print("   • 改进幅度瀑布图...")
    create_improvement_waterfall()
    print("   ✅ 已保存: improvement_waterfall.png")
    
    print("   • 场景-策略匹配矩阵...")
    create_scenario_strategy_matrix()
    print("   ✅ 已保存: scenario_strategy_matrix.png")
    
    print(f"\n🎉 增强对比度可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • enhanced_contrast_results.png - 高对比度主图(包含热力图)")
    print(f"   • strategy_advantage_radar.png - 策略优势雷达图")
    print(f"   • improvement_waterfall.png - 改进幅度瀑布图")
    print(f"   • scenario_strategy_matrix.png - 场景-策略匹配矩阵")
    
    print(f"\n💡 第四个图的推荐选择:")
    print(f"   1. 热力图 - 直观显示所有策略在各场景的表现")
    print(f"   2. 瀑布图 - 清晰展示改进幅度")
    print(f"   3. 匹配矩阵 - 突出最佳策略选择")

if __name__ == "__main__":
    main()
