#!/usr/bin/env python3
"""
快速Gemini API测试
"""

import google.generativeai as genai
import time

def quick_test():
    print("🚀 快速Gemini API测试")
    print("=" * 30)
    
    # 直接配置API
    api_key = "AIzaSyCsxMdx35FyTMGrLt_NUDJUKSinQ7CNPg4"
    print(f"🔑 使用API密钥: {api_key[:20]}...")
    
    try:
        genai.configure(api_key=api_key)
        print("✅ API配置成功")
        
        # 测试最基本的模型
        print("\n🔄 测试 gemini-pro 模型...")
        model = genai.GenerativeModel('gemini-pro')
        print("✅ 模型创建成功")
        
        # 最简单的测试
        print("📞 发送测试请求...")
        start_time = time.time()
        
        response = model.generate_content(
            "请用中文回答：北京是中国的首都吗？",
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=50,
                temperature=0.1,
            )
        )
        
        end_time = time.time()
        
        print(f"✅ 请求成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"📝 回答: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎉 Gemini API测试成功!")
        print("✅ 可以开始使用验证系统")
    else:
        print("\n❌ API测试失败")
        print("请检查:")
        print("1. 网络连接")
        print("2. API密钥是否有效")
        print("3. 是否有API配额")
