# 负责知识图谱更新逻辑。
import logging
from typing import Dict, List
from neo4j_crud import Neo4jCRUD
from datetime import datetime, timezone, timedelta

logger = logging.getLogger(__name__)

class KnowledgeGraphUpdater:
    def __init__(self, neo4j_conn):
        self.crud = Neo4jCRUD(neo4j_conn)

    def preprocess_data(self, data: Dict) -> Dict:
        """预处理数据以处理潜在的重复或规范化名称"""
        if data.get("name") == "纳木错":
            data["name"] = "纳木措"
        return data

    def create_nodes_only(self, data: Dict, log_id: str, reason: str, weights: Dict):
        """仅创建或更新节点，不创建关系"""
        if not data.get("name"):
            logger.error(f"无效数据，缺少 'name' 字段: {data}")
            raise ValueError("Data must include 'name' field")
        try:
            with self.crud.driver.session() as session:
                session.execute_write(self._create_nodes_tx, self.preprocess_data(data), reason, log_id, weights)
        except Exception as e:
            logger.error(f"创建节点失败 for {data.get('name', 'Unknown')}: {e}", exc_info=True)
            raise

    def _create_nodes_tx(self, tx, data, reason, log_id, weights):
        """事务函数：创建或更新节点"""
        # 创建或更新 Attraction 节点
        attraction_data = {
            "name": data.get("name", ""),
            "location": data.get("location", "拉萨市"),
            "address": data.get("address", ""),
            "description": data.get("description", f"Description for {data.get('name', 'Unknown')}"),
            "pub_timestamp": data.get("pub_timestamp", datetime.now(timezone(timedelta(hours=8))).isoformat() + "+08:00"),
            "best_comment": data.get("best_comment", ""),
            "source_type": data.get("source_type", "crawler"),
            "metrics": data.get("metrics", "{}"),
            "ranking": data.get("ranking", ""),
            "visitor_percentage": data.get("visitor_percentage", "")
        }
        if not attraction_data["name"] or not attraction_data["location"]:
            logger.warning(f"跳过处理，缺少必要字段: name={attraction_data['name']}, location={attraction_data['location']}")
            return
        self.crud.create_or_update_entity(tx, attraction_data, reason)
        logger.debug(f"更新 Attraction 节点: {attraction_data['name']}")

        # 创建或更新 City 节点
        city_data = {"name": attraction_data["location"]}
        self.crud.create_or_update_entity(tx, city_data, reason)
        logger.debug(f"更新 City 节点: {city_data['name']}")

        # 记录更新日志
        log_data = {
            "name": log_id,
            "log_id": log_id,
            "entity_name": attraction_data["name"],
            "reason": reason,
            "weights": str(weights),
            "timestamp": datetime.now(timezone(timedelta(hours=8))).isoformat()
        }
        self.crud.create_or_update_entity(tx, log_data, reason)
        logger.debug(f"创建 UpdateLog 节点: {log_id}")

    def update_knowledge_graph(self, data: Dict, log_id: str, reason: str, weights: Dict, all_nodes: List[Dict] = None):
        """更新知识图谱：创建节点和 LOCATED_IN 关系"""
        if not data.get("name"):
            logger.error(f"无效数据，缺少 'name' 字段: {data}")
            raise ValueError("Data must include 'name' field")
        try:
            with self.crud.driver.session() as session:
                session.execute_write(self.tx_work, self.preprocess_data(data), reason, log_id, weights)
        except Exception as e:
            logger.error(f"更新知识图谱失败 for {data.get('name', 'Unknown')}: {e}", exc_info=True)
            raise

    def tx_work(self, tx, data, reason, log_id, weights):
        """事务函数：创建节点和 LOCATED_IN 关系"""
        # 创建节点（与 create_nodes_only 一致）
        self._create_nodes_tx(tx, data, reason, log_id, weights)

        # 创建 LOCATED_IN 关系
        attraction_data = {
            "name": data.get("name", ""),
            "location": data.get("location", "拉萨市")
        }
        city_data = {"name": attraction_data["location"]}
        try:
            self.crud.create_relationship(
                tx=tx,
                source_label="Attraction",
                source_name=attraction_data["name"],
                target_label="City",
                target_name=city_data["name"],
                rel_type="LOCATED_IN",
                properties={"reason": reason}
            )
            logger.debug(f"创建 LOCATED_IN 关系: {attraction_data['name']} -> {city_data['name']}")
        except Exception as e:
            logger.error(f"创建 LOCATED_IN 关系失败: {attraction_data['name']} -> {city_data['name']}: {str(e)}")
            raise

    def close(self):
        self.crud.close()