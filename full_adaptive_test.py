#!/usr/bin/env python3
"""
完整的自适应策略测试 - 在所有100个案例上进行对比
"""

import json
from datetime import datetime
from typing import List, Dict
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

class AdaptiveHybridResolver:
    """自适应混合冲突解决器 - 简化版本用于批量测试"""
    
    def __init__(self):
        # 场景识别阈值
        self.weight_significant_threshold = 0.20
        self.time_significant_threshold = 180
        self.time_critical_threshold = 300
        
        # 不同场景的权重配置
        self.scenario_weights = {
            'weight_dominant': {'weight': 0.75, 'time': 0.20, 'content': 0.05},
            'time_dominant': {'weight': 0.20, 'time': 0.75, 'content': 0.05},
            'complex_tradeoff_favor_authority': {'weight': 0.60, 'time': 0.35, 'content': 0.05},
            'complex_tradeoff_favor_recency': {'weight': 0.35, 'time': 0.60, 'content': 0.05},
            'edge_case': {'weight': 0.45, 'time': 0.35, 'content': 0.20},
            'balanced': {'weight': 0.50, 'time': 0.40, 'content': 0.10}
        }
    
    def identify_scenario(self, conflicts: List[Dict]) -> str:
        """智能场景识别"""
        if len(conflicts) != 2:
            return 'balanced'
        
        c1, c2 = conflicts
        
        # 计算权重差异
        weight1 = c1['source']['weight']
        weight2 = c2['source']['weight']
        weight_diff = abs(weight1 - weight2)
        
        # 计算时间差异
        time1 = parse_timestamp(c1['timestamp'])
        time2 = parse_timestamp(c2['timestamp'])
        time_diff_days = abs((time1 - time2).days)
        
        # 计算内容质量差异
        content1_len = len(c1.get('value', ''))
        content2_len = len(c2.get('value', ''))
        content_diff = abs(content1_len - content2_len)
        
        # 场景识别逻辑
        if weight_diff >= self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            return 'weight_dominant'
        elif time_diff_days >= self.time_critical_threshold and weight_diff < self.weight_significant_threshold:
            return 'time_dominant'
        elif weight_diff >= self.weight_significant_threshold and time_diff_days >= self.time_significant_threshold:
            if time_diff_days >= self.time_critical_threshold:
                return 'complex_tradeoff_favor_recency'
            else:
                return 'complex_tradeoff_favor_authority'
        elif weight_diff < self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            if content_diff > 50:
                return 'edge_case'
            else:
                return 'balanced'
        else:
            return 'balanced'
    
    def calculate_enhanced_time_score(self, timestamp: str, scenario: str) -> float:
        """增强的时间评分机制"""
        time_obj = parse_timestamp(timestamp)
        now = datetime.now(time_obj.tzinfo)
        days_old = (now - time_obj).days
        
        if scenario in ['time_dominant', 'complex_tradeoff_favor_recency']:
            decay_factor = 365
            time_score = max(0, 1 - (days_old / decay_factor) ** 1.5)
        elif scenario == 'weight_dominant':
            decay_factor = 1095
            time_score = max(0, 1 - days_old / decay_factor)
        else:
            decay_factor = 730
            time_score = max(0, 1 - days_old / decay_factor)
        
        return time_score
    
    def calculate_enhanced_content_score(self, content: str, scenario: str) -> float:
        """增强的内容质量评分"""
        content_length = len(content)
        
        if scenario == 'edge_case':
            base_score = min(1.0, content_length / 80)
            if '详细' in content or '完整' in content or '权威' in content:
                base_score += 0.1
            if '官方' in content or '认证' in content:
                base_score += 0.1
            return min(1.0, base_score)
        else:
            return min(1.0, content_length / 100)
    
    def resolve_conflict(self, conflicts: List[Dict]) -> Dict:
        """自适应冲突解决主函数"""
        if not conflicts:
            return {}
        
        if len(conflicts) == 1:
            return conflicts[0]
        
        # 1. 场景识别
        scenario = self.identify_scenario(conflicts)
        weights_config = self.scenario_weights[scenario]
        
        # 2. 计算每个冲突的综合评分
        best_conflict = None
        best_score = -1
        
        for conflict in conflicts:
            weight = conflict['source']['weight']
            timestamp = conflict['timestamp']
            content = conflict.get('value', '')
            
            time_score = self.calculate_enhanced_time_score(timestamp, scenario)
            content_score = self.calculate_enhanced_content_score(content, scenario)
            
            final_score = (weight * weights_config['weight'] + 
                          time_score * weights_config['time'] + 
                          content_score * weights_config['content'])
            
            if final_score > best_score:
                best_score = final_score
                best_conflict = conflict
        
        return best_conflict

def resolve_conflict_original_hybrid(conflicts: List[Dict]) -> Dict:
    """原始混合策略"""
    if not conflicts:
        return {}
    
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict.get('source', {}).get('weight', 0)
        timestamp = parse_timestamp(conflict.get('timestamp', ''))
        content_length = len(conflict.get('value', ''))
        
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)
        content_score = min(1.0, content_length / 100)
        
        final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def resolve_conflict_latest_first(conflicts: List[Dict]) -> Dict:
    """时间优先策略"""
    if not conflicts:
        return {}
    return max(conflicts, key=lambda x: parse_timestamp(x.get('timestamp', '')))

def test_strategy(test_cases, strategy_func, strategy_name):
    """测试单个策略"""
    correct_count = 0
    results_by_type = defaultdict(lambda: {'correct': 0, 'total': 0})
    results_by_difficulty = defaultdict(lambda: {'correct': 0, 'total': 0})
    scenario_distribution = defaultdict(int)
    
    for case in test_cases:
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        conflict_type = case['conflict_type']
        difficulty = case['difficulty']
        
        # 应用策略
        if hasattr(strategy_func, 'resolve_conflict'):
            winner = strategy_func.resolve_conflict(conflicts)
            # 记录场景识别
            scenario = strategy_func.identify_scenario(conflicts)
            scenario_distribution[scenario] += 1
        else:
            winner = strategy_func(conflicts)
        
        winner_id = winner.get('source', {}).get('url', '') if winner else ''
        
        is_correct = winner_id == expected_id
        if is_correct:
            correct_count += 1
        
        # 按类型统计
        results_by_type[conflict_type]['total'] += 1
        if is_correct:
            results_by_type[conflict_type]['correct'] += 1
        
        # 按难度统计
        results_by_difficulty[difficulty]['total'] += 1
        if is_correct:
            results_by_difficulty[difficulty]['correct'] += 1
    
    accuracy = correct_count / len(test_cases) if test_cases else 0
    
    return {
        'strategy': strategy_name,
        'accuracy': accuracy,
        'correct_count': correct_count,
        'total_count': len(test_cases),
        'by_type': dict(results_by_type),
        'by_difficulty': dict(results_by_difficulty),
        'scenario_distribution': dict(scenario_distribution)
    }

def main():
    """主函数"""
    print("🚀 完整自适应策略对比测试 (100个案例)")
    print("=" * 80)
    
    # 加载测试数据
    test_cases = load_test_dataset()
    
    # 初始化策略
    adaptive_resolver = AdaptiveHybridResolver()
    
    # 测试所有策略
    strategies = [
        (resolve_conflict_original_hybrid, "原始Hybrid"),
        (resolve_conflict_latest_first, "Latest-First"),
        (adaptive_resolver, "自适应Hybrid")
    ]
    
    all_results = []
    
    for strategy_func, strategy_name in strategies:
        print(f"\n🔄 测试策略: {strategy_name}")
        result = test_strategy(test_cases, strategy_func, strategy_name)
        all_results.append(result)
        
        print(f"   整体准确率: {result['accuracy']:.3f} ({result['correct_count']}/{result['total_count']})")
    
    # 详细对比分析
    print(f"\n📊 详细对比分析")
    print("=" * 80)
    
    # 整体性能对比
    print(f"\n📈 整体性能对比:")
    print(f"{'策略':<15} {'准确率':<10} {'正确数':<10} {'改进幅度':<10}")
    print("-" * 50)
    
    baseline_acc = all_results[0]['accuracy']  # 原始Hybrid作为基准
    
    for result in all_results:
        improvement = result['accuracy'] - baseline_acc
        improvement_str = f"+{improvement:.3f}" if improvement > 0 else f"{improvement:.3f}"
        print(f"{result['strategy']:<15} {result['accuracy']:.3f}     {result['correct_count']:<10} {improvement_str:<10}")
    
    # 按冲突类型对比
    print(f"\n📊 按冲突类型对比:")
    print(f"{'冲突类型':<20} {'原始Hybrid':<12} {'Latest-First':<15} {'自适应Hybrid':<15}")
    print("-" * 65)
    
    conflict_types = ['weight_dominant', 'time_dominant', 'complex_tradeoff', 'edge_case']
    
    for conflict_type in conflict_types:
        row = f"{conflict_type:<20}"
        for result in all_results:
            if conflict_type in result['by_type']:
                stats = result['by_type'][conflict_type]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                row += f" {acc:.3f}({stats['correct']}/{stats['total']})"
                row += " " * (15 - len(f"{acc:.3f}({stats['correct']}/{stats['total']})"))
            else:
                row += f"{'N/A':<15}"
        print(row)
    
    # 按难度对比
    print(f"\n📊 按难度等级对比:")
    print(f"{'难度等级':<15} {'原始Hybrid':<15} {'Latest-First':<15} {'自适应Hybrid':<15}")
    print("-" * 65)
    
    difficulties = ['easy', 'medium', 'hard']
    
    for difficulty in difficulties:
        row = f"{difficulty:<15}"
        for result in all_results:
            if difficulty in result['by_difficulty']:
                stats = result['by_difficulty'][difficulty]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                row += f" {acc:.3f}({stats['correct']}/{stats['total']})"
                row += " " * (15 - len(f"{acc:.3f}({stats['correct']}/{stats['total']})"))
            else:
                row += f"{'N/A':<15}"
        print(row)
    
    # 自适应策略的场景识别分析
    adaptive_result = all_results[2]  # 自适应策略结果
    if 'scenario_distribution' in adaptive_result:
        print(f"\n🎯 自适应策略场景识别分布:")
        total_cases = sum(adaptive_result['scenario_distribution'].values())
        for scenario, count in adaptive_result['scenario_distribution'].items():
            percentage = count / total_cases * 100
            print(f"   {scenario}: {count}个 ({percentage:.1f}%)")
    
    # 关键发现
    print(f"\n🏆 关键发现:")
    print("=" * 60)
    
    best_overall = max(all_results, key=lambda x: x['accuracy'])
    print(f"1. 最佳整体策略: {best_overall['strategy']} (准确率: {best_overall['accuracy']:.3f})")
    
    # 困难场景最佳
    best_hard = max(all_results, 
                   key=lambda x: x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / 
                                x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total'])
    hard_acc = best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / \
               best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total']
    print(f"2. 困难场景最佳: {best_hard['strategy']} (准确率: {hard_acc:.3f})")
    
    # 改进幅度
    adaptive_acc = all_results[2]['accuracy']
    original_acc = all_results[0]['accuracy']
    improvement = adaptive_acc - original_acc
    print(f"3. 自适应策略改进幅度: {improvement:+.3f} ({improvement*100:+.1f}%)")
    
    print(f"\n💡 自适应策略的优势:")
    print(f"   • 智能场景识别，根据具体情况调整策略")
    print(f"   • 动态权重分配，避免固定权重的局限性")
    print(f"   • 增强的时间和内容评分机制")
    print(f"   • 透明的决策过程，便于调试和优化")

if __name__ == "__main__":
    main()
