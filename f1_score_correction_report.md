
# F1分数计算修正报告

## 🎯 问题发现
在原始实验中，F1分数的计算存在概念混淆：
- 错误地假设精确率等于准确率
- 错误地假设召回率为1.0
- 没有正确定义正负例

## 📊 正确的F1分数计算结果

### 各策略详细指标

#### Weight-First
- **真正例 (TP)**: 17
- **假正例 (FP)**: 9  
- **假负例 (FN)**: 9
- **精确率**: 0.654
- **召回率**: 0.654
- **F1分数**: 0.654
- **准确率**: 0.654

#### Latest-First
- **真正例 (TP)**: 14
- **假正例 (FP)**: 12  
- **假负例 (FN)**: 12
- **精确率**: 0.538
- **召回率**: 0.538
- **F1分数**: 0.538
- **准确率**: 0.538

#### Hybrid
- **真正例 (TP)**: 19
- **假正例 (FP)**: 7  
- **假负例 (FN)**: 7
- **精确率**: 0.731
- **召回率**: 0.731
- **F1分数**: 0.731
- **准确率**: 0.731

## 🏆 结论

### 最佳策略
**Hybrid** 在F1分数上表现最佳：0.731

### 重要发现
在冲突解决任务中，由于每个冲突都必须做出决策（不存在拒绝决策的选项），
因此：
- 精确率 = 召回率 = 准确率 = 正确决策数 / 总决策数
- F1分数 = 准确率

这意味着在我们的实验设定下，**F1分数实际上就等于准确率**。

### 建议
1. 在冲突解决任务中，准确率已经是最直接有效的评估指标
2. 如果要使F1分数更有意义，需要重新设计任务，允许"拒绝决策"选项
3. 或者改为多分类任务，评估选择不同冲突解决策略的效果

---
报告生成时间: 2025-07-22 11:14:19
