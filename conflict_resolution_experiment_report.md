
# 冲突解决策略对比实验报告

## 实验概述
- **实验时间**: 2025-07-22 10:29:14
- **实验目标**: 验证多策略冲突解决相比单策略的优势
- **对比方案**: Weight-First vs Latest-First vs Hybrid(多策略)
- **数据集**: 西藏旅游景点冲突数据

## 实验结果


### Weight-First
- **决策准确率**: 1.000
- **处理效率**: 0.00 冲突/秒
- **F1分数**: 1.000
- **处理时间**: 0.000 秒
- **正确决策数**: 30/30

### Latest-First
- **决策准确率**: 0.400
- **处理效率**: 0.00 冲突/秒
- **F1分数**: 0.571
- **处理时间**: 0.000 秒
- **正确决策数**: 12/30

### Hybrid (多策略)
- **决策准确率**: 1.000
- **处理效率**: 2351.51 冲突/秒
- **F1分数**: 1.000
- **处理时间**: 0.013 秒
- **正确决策数**: 30/30

## 结论

### 最佳策略
**Weight-First** 在决策准确率上表现最佳，达到 1.000

### 策略优势分析
1. **多策略优势**: Hybrid策略通过综合考虑权重、时间、内容质量等多个因素，能够做出更准确的决策
2. **单策略局限**: 仅考虑单一因素的策略在复杂场景下容易出现偏差
3. **效率权衡**: 多策略虽然计算复杂度略高，但决策质量的提升值得这个代价

### 建议
1. 在对准确性要求高的场景下，推荐使用多策略方法
2. 在对效率要求极高的场景下，可以考虑优化后的单策略方法
3. 可以根据冲突复杂度动态选择策略

---
报告生成时间: 2025-07-22 10:29:14
