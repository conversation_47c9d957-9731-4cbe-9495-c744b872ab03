#!/usr/bin/env python3
"""
基于拉萨知识图谱数据测试冲突解决策略

使用从真实拉萨景点数据生成的测试集，评估三种策略的性能
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    try:
        with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载测试数据失败: {e}")
        return []

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

def resolve_conflict_weight_first(conflicts: List[Dict]) -> Dict:
    """权重优先策略"""
    if not conflicts:
        return {}
    
    # 按权重排序，选择权重最高的
    return max(conflicts, key=lambda x: x.get('source', {}).get('weight', 0))

def resolve_conflict_latest_first(conflicts: List[Dict]) -> Dict:
    """时间优先策略"""
    if not conflicts:
        return {}
    
    # 按时间排序，选择最新的
    return max(conflicts, key=lambda x: parse_timestamp(x.get('timestamp', '')))

def resolve_conflict_hybrid(conflicts: List[Dict]) -> Dict:
    """混合策略"""
    if not conflicts:
        return {}
    
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict.get('source', {}).get('weight', 0)
        timestamp = parse_timestamp(conflict.get('timestamp', ''))
        content_length = len(conflict.get('value', ''))
        
        # 时间新鲜度评分 (2年内线性衰减)
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)
        
        # 内容质量评分
        content_score = min(1.0, content_length / 100)
        
        # 综合评分: 权重50% + 时间30% + 内容20%
        final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def run_strategy_test(test_cases: List[Dict], strategy_func, strategy_name: str):
    """运行单个策略的测试"""
    print(f"\n🔄 测试策略: {strategy_name}")
    
    correct_count = 0
    results = []
    start_time = time.time()
    
    for case in test_cases:
        conflicts = case['conflicts']
        expected_winner_id = case['expected_winner_id']
        
        # 应用策略
        winner = strategy_func(conflicts)
        winner_id = winner.get('source', {}).get('url', '') if winner else ''
        
        is_correct = winner_id == expected_winner_id
        if is_correct:
            correct_count += 1
        
        results.append({
            'case_id': case['id'],
            'case_name': case['name'],
            'conflict_type': case['conflict_type'],
            'difficulty': case['difficulty'],
            'expected': expected_winner_id,
            'chosen': winner_id,
            'correct': is_correct
        })
    
    end_time = time.time()
    execution_time = end_time - start_time
    accuracy = correct_count / len(test_cases) if test_cases else 0
    
    print(f"   ✅ 准确率: {accuracy:.3f} ({correct_count}/{len(test_cases)})")
    print(f"   ⏱️  执行时间: {execution_time:.4f}秒")
    
    return {
        'strategy': strategy_name,
        'accuracy': accuracy,
        'correct_count': correct_count,
        'total_count': len(test_cases),
        'execution_time': execution_time,
        'results': results
    }

def analyze_by_conflict_type(all_results: List[Dict]):
    """按冲突类型分析结果"""
    print(f"\n📊 按冲突类型分析")
    print("=" * 70)
    
    # 收集各策略在不同冲突类型下的表现
    type_stats = defaultdict(lambda: defaultdict(list))
    
    for strategy_result in all_results:
        strategy_name = strategy_result['strategy']
        for result in strategy_result['results']:
            conflict_type = result['conflict_type']
            is_correct = result['correct']
            type_stats[conflict_type][strategy_name].append(is_correct)
    
    # 打印结果表格
    print(f"{'冲突类型':<20} {'Weight-First':<15} {'Latest-First':<15} {'Hybrid':<15}")
    print("-" * 70)
    
    for conflict_type in sorted(type_stats.keys()):
        row = f"{conflict_type:<20}"
        
        for strategy in ["Weight-First", "Latest-First", "Hybrid"]:
            if strategy in type_stats[conflict_type]:
                correct_list = type_stats[conflict_type][strategy]
                accuracy = sum(correct_list) / len(correct_list)
                count_str = f"{sum(correct_list)}/{len(correct_list)}"
                row += f" {accuracy:.3f}({count_str})"
                row += " " * (15 - len(f"{accuracy:.3f}({count_str})"))
            else:
                row += f"{'N/A':<15}"
        
        print(row)
    
    return type_stats

def analyze_by_difficulty(all_results: List[Dict]):
    """按难度分析结果"""
    print(f"\n📊 按难度等级分析")
    print("=" * 65)
    
    # 收集各策略在不同难度下的表现
    difficulty_stats = defaultdict(lambda: defaultdict(list))
    
    for strategy_result in all_results:
        strategy_name = strategy_result['strategy']
        for result in strategy_result['results']:
            difficulty = result['difficulty']
            is_correct = result['correct']
            difficulty_stats[difficulty][strategy_name].append(is_correct)
    
    # 打印结果表格
    print(f"{'难度等级':<15} {'Weight-First':<15} {'Latest-First':<15} {'Hybrid':<15}")
    print("-" * 65)
    
    for difficulty in ["easy", "medium", "hard"]:
        if difficulty in difficulty_stats:
            row = f"{difficulty:<15}"
            
            for strategy in ["Weight-First", "Latest-First", "Hybrid"]:
                if strategy in difficulty_stats[difficulty]:
                    correct_list = difficulty_stats[difficulty][strategy]
                    accuracy = sum(correct_list) / len(correct_list)
                    count_str = f"{sum(correct_list)}/{len(correct_list)}"
                    row += f" {accuracy:.3f}({count_str})"
                    row += " " * (15 - len(f"{accuracy:.3f}({count_str})"))
                else:
                    row += f"{'N/A':<15}"
            
            print(row)

def analyze_error_patterns(all_results: List[Dict]):
    """分析错误模式"""
    print(f"\n🔍 错误模式分析")
    print("=" * 60)
    
    for strategy_result in all_results:
        strategy_name = strategy_result['strategy']
        errors = [r for r in strategy_result['results'] if not r['correct']]
        
        if errors:
            print(f"\n❌ {strategy_name} 错误分析 (共{len(errors)}个错误):")
            
            # 按冲突类型统计错误
            error_by_type = defaultdict(int)
            error_by_difficulty = defaultdict(int)
            
            for error in errors:
                error_by_type[error['conflict_type']] += 1
                error_by_difficulty[error['difficulty']] += 1
            
            print(f"   错误分布 (按类型): {dict(error_by_type)}")
            print(f"   错误分布 (按难度): {dict(error_by_difficulty)}")
            
            # 显示几个典型错误案例
            print(f"   典型错误案例:")
            for i, error in enumerate(errors[:3]):
                print(f"     {i+1}. {error['case_name']}")
                print(f"        期望: {error['expected']}")
                print(f"        实际: {error['chosen']}")

def create_summary_report(all_results: List[Dict], test_cases: List[Dict]):
    """创建总结报告"""
    print(f"\n📋 实验总结报告")
    print("=" * 80)
    
    print(f"🎯 基于拉萨知识图谱的冲突解决策略测试")
    print(f"📊 测试数据: {len(test_cases)} 个基于真实景点数据的冲突案例")
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📈 整体性能对比:")
    print(f"{'策略':<15} {'准确率':<10} {'正确数':<10} {'执行时间':<12}")
    print("-" * 50)
    
    best_accuracy = 0
    best_strategy = ""
    
    for result in all_results:
        accuracy = result['accuracy']
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            best_strategy = result['strategy']
        
        print(f"{result['strategy']:<15} {accuracy:.3f}     {result['correct_count']:<10} {result['execution_time']:.4f}s")
    
    print(f"\n🏆 最佳策略: {best_strategy} (准确率: {best_accuracy:.3f})")
    
    # 计算改进幅度
    weight_first_acc = next(r['accuracy'] for r in all_results if r['strategy'] == 'Weight-First')
    latest_first_acc = next(r['accuracy'] for r in all_results if r['strategy'] == 'Latest-First')
    hybrid_acc = next(r['accuracy'] for r in all_results if r['strategy'] == 'Hybrid')
    
    best_single = max(weight_first_acc, latest_first_acc)
    improvement = hybrid_acc - best_single
    
    print(f"\n📊 关键发现:")
    print(f"   • Weight-First准确率: {weight_first_acc:.3f}")
    print(f"   • Latest-First准确率: {latest_first_acc:.3f}")
    print(f"   • Hybrid准确率: {hybrid_acc:.3f}")
    
    if improvement > 0:
        print(f"   ✅ Hybrid策略相比最佳单策略提升: {improvement:.3f} ({improvement*100:.1f}%)")
    else:
        print(f"   ⚠️  Hybrid策略需要进一步优化")

def main():
    """主函数"""
    print("🚀 基于拉萨知识图谱的冲突解决策略测试")
    print("=" * 80)
    
    # 加载测试数据
    test_cases = load_test_dataset()
    if not test_cases:
        print("❌ 无法加载测试数据，程序退出")
        return
    
    print(f"✅ 成功加载 {len(test_cases)} 个测试案例")
    
    # 统计测试集信息
    type_counts = defaultdict(int)
    difficulty_counts = defaultdict(int)
    
    for case in test_cases:
        type_counts[case['conflict_type']] += 1
        difficulty_counts[case['difficulty']] += 1
    
    print(f"📊 测试集分布:")
    print(f"   冲突类型: {dict(type_counts)}")
    print(f"   难度分布: {dict(difficulty_counts)}")
    
    # 定义策略
    strategies = [
        (resolve_conflict_weight_first, "Weight-First"),
        (resolve_conflict_latest_first, "Latest-First"),
        (resolve_conflict_hybrid, "Hybrid")
    ]
    
    # 运行所有策略测试
    all_results = []
    for strategy_func, strategy_name in strategies:
        result = run_strategy_test(test_cases, strategy_func, strategy_name)
        all_results.append(result)
    
    # 详细分析
    analyze_by_conflict_type(all_results)
    analyze_by_difficulty(all_results)
    analyze_error_patterns(all_results)
    create_summary_report(all_results, test_cases)
    
    print(f"\n🎉 测试完成！")
    print(f"📁 基于您的拉萨知识图谱数据生成的测试集: lhasa_conflict_test_dataset.json")

if __name__ == "__main__":
    main()
