#!/usr/bin/env python3
"""
测试Gemini 2.5 Pro API连接

验证API密钥是否正确配置，模型是否可以正常调用
"""

import os
import google.generativeai as genai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('config.env')

def test_gemini_api():
    """测试Gemini API连接"""
    
    print("🔧 测试Gemini 2.5 Pro API连接")
    print("=" * 50)
    
    # 获取API密钥
    api_key = os.getenv('GOOGLE_API_KEY')
    model_name = os.getenv('GOOGLE_MODEL', 'gemini-2.0-flash-exp')
    
    if not api_key:
        print("❌ 未找到GOOGLE_API_KEY环境变量")
        return False
    
    print(f"📋 API密钥: {api_key[:20]}...")
    print(f"📋 模型名称: {model_name}")
    
    try:
        # 配置API
        genai.configure(api_key=api_key)
        
        # 创建模型实例
        model = genai.GenerativeModel(model_name)
        
        print(f"✅ 模型实例创建成功")
        
        # 测试简单查询
        print(f"\n🔄 测试简单查询...")
        
        test_prompt = """
请简单回答：你是什么模型？请用中文回答。
"""
        
        response = model.generate_content(
            test_prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,
                max_output_tokens=100,
            )
        )
        
        print(f"✅ API调用成功")
        print(f"📝 模型回答: {response.text}")
        
        # 测试冲突判断格式
        print(f"\n🔄 测试冲突判断格式...")
        
        judgment_prompt = """
你是一位信息质量评估专家。请分析以下两个冲突的信息源，选择更可靠的一个。

## 冲突信息：
**信息源A**:
- 实体: 布达拉宫
- 内容: 世界文化遗产，藏传佛教圣地（官方权威认证）
- 来源类型: government
- 权重: 0.95

**信息源B**:
- 实体: 布达拉宫
- 内容: 听说布达拉宫还行吧，没去过
- 来源类型: personal_blog
- 权重: 0.25

## 请按以下格式回答：
**选择**: A 或 B
**置信度**: 1-10分 (10分最确信)
**理由**: 详细说明选择原因

请确保回答格式严格按照上述要求。
"""
        
        response = model.generate_content(
            judgment_prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=0.1,
                max_output_tokens=500,
            )
        )
        
        print(f"✅ 冲突判断测试成功")
        print(f"📝 判断结果:")
        print(response.text)
        
        # 测试响应解析
        print(f"\n🔄 测试响应解析...")
        
        from multi_model_validator import MultiModelValidator
        validator = MultiModelValidator()
        
        choice, confidence, reasoning = validator.parse_model_response(response.text)
        
        print(f"✅ 响应解析成功")
        print(f"   选择: {choice}")
        print(f"   置信度: {confidence}")
        print(f"   理由: {reasoning[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        print(f"   错误类型: {type(e).__name__}")
        
        # 提供常见错误的解决方案
        if "API_KEY" in str(e):
            print(f"💡 解决方案: 检查API密钥是否正确")
        elif "quota" in str(e).lower():
            print(f"💡 解决方案: API配额不足，请检查账户余额")
        elif "model" in str(e).lower():
            print(f"💡 解决方案: 模型名称可能不正确，尝试使用 'gemini-pro'")
        
        return False

def test_available_models():
    """测试可用的模型列表"""
    
    print(f"\n🔍 查询可用模型...")
    
    try:
        api_key = os.getenv('GOOGLE_API_KEY')
        genai.configure(api_key=api_key)
        
        # 列出可用模型
        models = genai.list_models()
        
        print(f"📋 可用模型列表:")
        for model in models:
            if 'gemini' in model.name.lower():
                print(f"   • {model.name}")
                if hasattr(model, 'description'):
                    print(f"     描述: {model.description}")
        
    except Exception as e:
        print(f"❌ 查询模型列表失败: {e}")

def main():
    """主函数"""
    
    # 测试API连接
    success = test_gemini_api()
    
    if success:
        print(f"\n🎉 Gemini API测试完全成功！")
        print(f"✅ 可以开始使用多模型验证系统")
        
        # 查询可用模型
        test_available_models()
        
    else:
        print(f"\n❌ Gemini API测试失败")
        print(f"请检查以下项目:")
        print(f"   1. API密钥是否正确")
        print(f"   2. 网络连接是否正常")
        print(f"   3. 模型名称是否正确")
        print(f"   4. API配额是否充足")

if __name__ == "__main__":
    main()
