#!/usr/bin/env python3
"""
自适应Hybrid策略 - 根据不同场景动态调整权重

基于对失败案例的深度分析，设计真正智能的多策略融合方法
"""

import json
import math
from datetime import datetime
from typing import List, Dict, Tuple
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

class AdaptiveHybridResolver:
    """自适应混合冲突解决器"""
    
    def __init__(self):
        # 场景识别阈值
        self.weight_significant_threshold = 0.20  # 权重差异显著阈值
        self.time_significant_threshold = 180     # 时间差异显著阈值(天)
        self.time_critical_threshold = 300        # 时间关键阈值(天)
        
        # 不同场景的权重配置
        self.scenario_weights = {
            'weight_dominant': {'weight': 0.75, 'time': 0.20, 'content': 0.05},
            'time_dominant': {'weight': 0.20, 'time': 0.75, 'content': 0.05},
            'complex_tradeoff_favor_authority': {'weight': 0.60, 'time': 0.35, 'content': 0.05},
            'complex_tradeoff_favor_recency': {'weight': 0.35, 'time': 0.60, 'content': 0.05},
            'edge_case': {'weight': 0.45, 'time': 0.35, 'content': 0.20},
            'balanced': {'weight': 0.50, 'time': 0.40, 'content': 0.10}
        }
    
    def identify_scenario(self, conflicts: List[Dict]) -> str:
        """智能场景识别"""
        if len(conflicts) != 2:
            return 'balanced'
        
        c1, c2 = conflicts
        
        # 计算权重差异
        weight1 = c1['source']['weight']
        weight2 = c2['source']['weight']
        weight_diff = abs(weight1 - weight2)
        max_weight = max(weight1, weight2)
        min_weight = min(weight1, weight2)
        
        # 计算时间差异
        time1 = parse_timestamp(c1['timestamp'])
        time2 = parse_timestamp(c2['timestamp'])
        time_diff_days = abs((time1 - time2).days)
        
        # 计算内容质量差异
        content1_len = len(c1.get('value', ''))
        content2_len = len(c2.get('value', ''))
        content_diff = abs(content1_len - content2_len)
        
        print(f"🔍 场景分析: 权重差{weight_diff:.3f}, 时间差{time_diff_days}天, 内容差{content_diff}字符")
        
        # 场景识别逻辑
        if weight_diff >= self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            # 权重差异显著，时间差异不大 -> 权重主导
            return 'weight_dominant'
        
        elif time_diff_days >= self.time_critical_threshold and weight_diff < self.weight_significant_threshold:
            # 时间差异很大，权重差异不大 -> 时间主导
            return 'time_dominant'
        
        elif weight_diff >= self.weight_significant_threshold and time_diff_days >= self.time_significant_threshold:
            # 权重和时间都有显著差异 -> 复杂权衡
            if time_diff_days >= self.time_critical_threshold:
                # 时间差异非常大，偏向时效性
                return 'complex_tradeoff_favor_recency'
            else:
                # 时间差异中等，偏向权威性
                return 'complex_tradeoff_favor_authority'
        
        elif weight_diff < self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            # 权重和时间差异都不大 -> 边界情况
            if content_diff > 50:  # 内容差异较大
                return 'edge_case'
            else:
                return 'balanced'
        
        else:
            # 其他情况使用平衡策略
            return 'balanced'
    
    def calculate_enhanced_time_score(self, timestamp: str, scenario: str) -> float:
        """增强的时间评分机制"""
        time_obj = parse_timestamp(timestamp)
        now = datetime.now(time_obj.tzinfo)
        days_old = (now - time_obj).days
        
        if scenario in ['time_dominant', 'complex_tradeoff_favor_recency']:
            # 时间敏感场景：更陡峭的衰减
            decay_factor = 365  # 1年内快速衰减
            time_score = max(0, 1 - (days_old / decay_factor) ** 1.5)
        elif scenario == 'weight_dominant':
            # 权重主导场景：时间不太重要
            decay_factor = 1095  # 3年内缓慢衰减
            time_score = max(0, 1 - days_old / decay_factor)
        else:
            # 其他场景：标准衰减
            decay_factor = 730  # 2年内线性衰减
            time_score = max(0, 1 - days_old / decay_factor)
        
        return time_score
    
    def calculate_enhanced_content_score(self, content: str, scenario: str) -> float:
        """增强的内容质量评分"""
        content_length = len(content)
        
        if scenario == 'edge_case':
            # 边界情况更重视内容质量
            base_score = min(1.0, content_length / 80)  # 更容易达到高分
            
            # 内容丰富度奖励
            if '详细' in content or '完整' in content or '权威' in content:
                base_score += 0.1
            if '官方' in content or '认证' in content:
                base_score += 0.1
                
            return min(1.0, base_score)
        else:
            # 其他场景标准评分
            return min(1.0, content_length / 100)
    
    def calculate_confidence_score(self, scenario: str, weight_diff: float, time_diff_days: int) -> float:
        """计算决策置信度"""
        if scenario == 'weight_dominant':
            return min(1.0, weight_diff / 0.5)  # 权重差异越大置信度越高
        elif scenario == 'time_dominant':
            return min(1.0, time_diff_days / 500)  # 时间差异越大置信度越高
        elif 'complex_tradeoff' in scenario:
            return 0.7  # 复杂权衡场景置信度中等
        elif scenario == 'edge_case':
            return 0.5  # 边界情况置信度较低
        else:
            return 0.6  # 平衡场景置信度中等
    
    def resolve_conflict(self, conflicts: List[Dict]) -> Dict:
        """自适应冲突解决主函数"""
        if not conflicts:
            return {}
        
        if len(conflicts) == 1:
            return conflicts[0]
        
        # 1. 场景识别
        scenario = self.identify_scenario(conflicts)
        weights_config = self.scenario_weights[scenario]
        
        print(f"📊 识别场景: {scenario}")
        print(f"🎯 权重配置: 权重{weights_config['weight']:.2f}, 时间{weights_config['time']:.2f}, 内容{weights_config['content']:.2f}")
        
        # 2. 计算每个冲突的综合评分
        best_conflict = None
        best_score = -1
        scores_detail = []
        
        for i, conflict in enumerate(conflicts):
            # 基础指标
            weight = conflict['source']['weight']
            timestamp = conflict['timestamp']
            content = conflict.get('value', '')
            
            # 增强评分
            time_score = self.calculate_enhanced_time_score(timestamp, scenario)
            content_score = self.calculate_enhanced_content_score(content, scenario)
            
            # 综合评分
            final_score = (weight * weights_config['weight'] + 
                          time_score * weights_config['time'] + 
                          content_score * weights_config['content'])
            
            scores_detail.append({
                'conflict_id': i,
                'weight': weight,
                'time_score': time_score,
                'content_score': content_score,
                'final_score': final_score,
                'source_type': conflict['source']['type'],
                'url': conflict['source']['url']
            })
            
            if final_score > best_score:
                best_score = final_score
                best_conflict = conflict
        
        # 3. 输出详细决策过程
        print(f"📋 评分详情:")
        for detail in scores_detail:
            print(f"   选项{detail['conflict_id']}: 权重{detail['weight']:.3f}, 时间{detail['time_score']:.3f}, 内容{detail['content_score']:.3f} -> 总分{detail['final_score']:.3f}")
        
        # 4. 计算置信度
        if len(conflicts) == 2:
            weight_diff = abs(conflicts[0]['source']['weight'] - conflicts[1]['source']['weight'])
            time1 = parse_timestamp(conflicts[0]['timestamp'])
            time2 = parse_timestamp(conflicts[1]['timestamp'])
            time_diff_days = abs((time1 - time2).days)
            confidence = self.calculate_confidence_score(scenario, weight_diff, time_diff_days)
            print(f"🎯 决策置信度: {confidence:.3f}")
        
        return best_conflict

def test_adaptive_strategy():
    """测试自适应策略"""
    test_cases = load_test_dataset()
    resolver = AdaptiveHybridResolver()
    
    print("🚀 自适应Hybrid策略测试")
    print("=" * 80)
    
    correct_count = 0
    results_by_scenario = defaultdict(lambda: {'correct': 0, 'total': 0})
    results_by_difficulty = defaultdict(lambda: {'correct': 0, 'total': 0})
    results_by_type = defaultdict(lambda: {'correct': 0, 'total': 0})
    
    scenario_distribution = defaultdict(int)
    
    for i, case in enumerate(test_cases[:20]):  # 先测试前20个案例
        print(f"\n🔬 测试案例 {i+1}: {case['name']}")
        print(f"   类型: {case['conflict_type']}, 难度: {case['difficulty']}")
        
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        
        # 应用自适应策略
        winner = resolver.resolve_conflict(conflicts)
        winner_id = winner.get('source', {}).get('url', '') if winner else ''
        
        # 识别的场景
        scenario = resolver.identify_scenario(conflicts)
        scenario_distribution[scenario] += 1
        
        is_correct = winner_id == expected_id
        if is_correct:
            correct_count += 1
            print(f"   ✅ 决策正确")
        else:
            print(f"   ❌ 决策错误")
            print(f"      期望: {expected_id}")
            print(f"      实际: {winner_id}")
        
        # 统计
        results_by_scenario[scenario]['total'] += 1
        if is_correct:
            results_by_scenario[scenario]['correct'] += 1
        
        results_by_difficulty[case['difficulty']]['total'] += 1
        if is_correct:
            results_by_difficulty[case['difficulty']]['correct'] += 1
        
        results_by_type[case['conflict_type']]['total'] += 1
        if is_correct:
            results_by_type[case['conflict_type']]['correct'] += 1
    
    # 输出结果
    accuracy = correct_count / 20
    print(f"\n📊 自适应策略测试结果")
    print(f"=" * 60)
    print(f"整体准确率: {accuracy:.3f} ({correct_count}/20)")
    
    print(f"\n📈 按识别场景分析:")
    for scenario, stats in results_by_scenario.items():
        if stats['total'] > 0:
            acc = stats['correct'] / stats['total']
            print(f"   {scenario}: {acc:.3f} ({stats['correct']}/{stats['total']})")
    
    print(f"\n📊 场景识别分布:")
    for scenario, count in scenario_distribution.items():
        percentage = count / 20 * 100
        print(f"   {scenario}: {count}个 ({percentage:.1f}%)")
    
    print(f"\n📋 按难度分析:")
    for difficulty, stats in results_by_difficulty.items():
        if stats['total'] > 0:
            acc = stats['correct'] / stats['total']
            print(f"   {difficulty}: {acc:.3f} ({stats['correct']}/{stats['total']})")
    
    return accuracy, results_by_scenario

def compare_with_original():
    """与原始策略对比"""
    print(f"\n🔄 与原始策略对比测试")
    print("=" * 60)
    
    # 这里可以加载原始策略的结果进行对比
    print("📊 预期改进:")
    print("   • 权重主导场景: 准确率应该接近100%")
    print("   • 时间主导场景: 准确率应该显著提升")
    print("   • 复杂权衡场景: 准确率应该有所改善")
    print("   • 边界情况: 通过内容质量评分改善")

def main():
    """主函数"""
    print("🧠 自适应Hybrid策略 - 智能场景感知与动态权重调整")
    print("=" * 80)
    
    # 测试自适应策略
    accuracy, scenario_results = test_adaptive_strategy()
    
    # 与原始策略对比
    compare_with_original()
    
    print(f"\n💡 自适应策略的核心优势:")
    print(f"=" * 60)
    print(f"1. 🎯 智能场景识别: 根据权重差异、时间差异、内容差异自动识别场景")
    print(f"2. 🔧 动态权重调整: 不同场景使用不同的权重配置")
    print(f"3. 📈 增强评分机制: 时间和内容评分根据场景优化")
    print(f"4. 🎲 置信度评估: 提供决策的可信度评估")
    print(f"5. 🔍 透明决策过程: 详细输出决策依据")

if __name__ == "__main__":
    main()
