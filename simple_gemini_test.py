#!/usr/bin/env python3
"""
简化的Gemini API测试
"""

import google.generativeai as genai

def test_gemini_simple():
    """简化的Gemini测试"""
    
    print("🔧 简化Gemini API测试")
    print("=" * 40)
    
    # 配置API密钥
    api_key = "AIzaSyCsxMdx35FyTMGrLt_NUDJUKSinQ7CNPg4"
    genai.configure(api_key=api_key)
    
    print(f"✅ API密钥配置完成")
    
    # 尝试不同的模型名称
    model_names = [
        'gemini-pro',
        'gemini-1.5-pro',
        'gemini-2.0-flash-exp',
        'gemini-1.5-flash'
    ]
    
    for model_name in model_names:
        print(f"\n🔄 测试模型: {model_name}")
        
        try:
            # 创建模型
            model = genai.GenerativeModel(model_name)
            print(f"   ✅ 模型创建成功")
            
            # 简单测试
            response = model.generate_content("你好，请用中文回答：1+1等于几？")
            print(f"   ✅ API调用成功")
            print(f"   📝 回答: {response.text}")
            
            # 如果成功，就使用这个模型
            return model_name, True
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            continue
    
    return None, False

def list_available_models():
    """列出可用模型"""
    print(f"\n🔍 查询可用模型...")
    
    try:
        api_key = "AIzaSyCsxMdx35FyTMGrLt_NUDJUKSinQ7CNPg4"
        genai.configure(api_key=api_key)
        
        models = genai.list_models()
        
        print(f"📋 可用模型:")
        for model in models:
            print(f"   • {model.name}")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    # 测试模型
    working_model, success = test_gemini_simple()
    
    if success:
        print(f"\n🎉 找到可用模型: {working_model}")
    else:
        print(f"\n❌ 所有模型都测试失败")
        
        # 尝试列出可用模型
        list_available_models()
