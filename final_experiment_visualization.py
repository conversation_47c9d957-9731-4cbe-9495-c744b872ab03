#!/usr/bin/env python3
"""
最终实验结果可视化

基于优化自适应策略的完整实验结果可视化
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import json

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def create_final_performance_chart():
    """创建最终性能对比图"""
    
    # 最终实验结果数据
    strategies = ['Weight-First', 'Latest-First', '原始Hybrid', '优化自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.810, 0.830]
    
    # 各场景下的表现
    weight_dominant = [1.000, 0.500, 1.000, 1.000]
    time_dominant = [0.160, 1.000, 0.560, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.760, 0.800]
    edge_case = [0.650, 0.350, 0.900, 0.900]
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000, 1.000]
    medium_performance = [0.722, 0.472, 0.889, 0.917]
    hard_performance = [0.591, 0.705, 0.659, 0.682]
    
    # 创建综合图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略最终实验结果', fontsize=16, fontweight='bold')
    
    # 1. 整体准确率对比
    colors = ['#FF6B6B', '#FFA07A', '#45B7D1', '#32CD32']
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors, alpha=0.8)
    ax1.set_title('整体准确率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('准确率')
    ax1.set_ylim(0, 1)
    ax1.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 按冲突类型分析
    x = np.arange(len(strategies))
    width = 0.2
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', alpha=0.8, color='#FF6B6B')
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', alpha=0.8, color='#FFA07A')
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', alpha=0.8, color='#45B7D1')
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', alpha=0.8, color='#32CD32')
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=14, fontweight='bold')
    ax2.set_ylabel('准确率')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, rotation=45)
    ax2.legend()
    ax2.set_ylim(0, 1.1)
    
    # 3. 按难度等级分析
    ax3.bar(x - width, easy_performance, width, label='简单', alpha=0.8, color='#90EE90')
    ax3.bar(x, medium_performance, width, label='中等', alpha=0.8, color='#FFD700')
    ax3.bar(x + width, hard_performance, width, label='困难', alpha=0.8, color='#FF6347')
    
    ax3.set_title('按难度等级的准确率分析', fontsize=14, fontweight='bold')
    ax3.set_ylabel('准确率')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies, rotation=45)
    ax3.legend()
    ax3.set_ylim(0, 1.1)
    
    # 4. 改进幅度分析
    baseline = overall_accuracy[0]  # Weight-First作为基准
    improvements = [acc - baseline for acc in overall_accuracy]
    
    colors_improvement = ['#808080', '#FF4444', '#4444FF', '#44FF44']
    bars4 = ax4.bar(strategies, improvements, color=colors_improvement, alpha=0.8)
    ax4.set_title('相比Weight-First的改进幅度', fontsize=14, fontweight='bold')
    ax4.set_ylabel('准确率改进')
    ax4.tick_params(axis='x', rotation=45)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加数值标签
    for bar, improvement in zip(bars4, improvements):
        height = bar.get_height()
        label = f'+{improvement:.3f}' if improvement > 0 else f'{improvement:.3f}'
        y_pos = height + 0.01 if height >= 0 else height - 0.02
        ax4.text(bar.get_x() + bar.get_width()/2., y_pos,
                label, ha='center', va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('final_experiment_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_final_radar_chart():
    """创建最终雷达图"""
    
    # 基于最终实验结果的指标
    categories = ['整体准确率', '权重主导场景', '时间主导场景', 
                 '复杂权衡场景', '边界情况处理', '简单场景', '中等场景', '困难场景']
    
    # 最终实验数据
    weight_first_scores = [0.630, 1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591]
    latest_first_scores = [0.560, 0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705]
    original_hybrid_scores = [0.810, 1.000, 0.560, 0.760, 0.900, 1.000, 0.889, 0.659]
    optimized_adaptive_scores = [0.830, 1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]
    
    # 计算角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    # 闭合数据
    weight_first_scores += weight_first_scores[:1]
    latest_first_scores += latest_first_scores[:1]
    original_hybrid_scores += original_hybrid_scores[:1]
    optimized_adaptive_scores += optimized_adaptive_scores[:1]
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # 绘制四条线
    ax.plot(angles, weight_first_scores, 'o-', linewidth=2, label='Weight-First', color='#FF6B6B')
    ax.fill(angles, weight_first_scores, alpha=0.15, color='#FF6B6B')
    
    ax.plot(angles, latest_first_scores, 'o-', linewidth=2, label='Latest-First', color='#FFA07A')
    ax.fill(angles, latest_first_scores, alpha=0.15, color='#FFA07A')
    
    ax.plot(angles, original_hybrid_scores, 'o-', linewidth=2, label='原始Hybrid', color='#45B7D1')
    ax.fill(angles, original_hybrid_scores, alpha=0.15, color='#45B7D1')
    
    ax.plot(angles, optimized_adaptive_scores, 'o-', linewidth=3, label='优化自适应Hybrid', color='#32CD32')
    ax.fill(angles, optimized_adaptive_scores, alpha=0.25, color='#32CD32')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    
    # 添加网格线
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    ax.grid(True)
    
    # 添加标题和图例
    plt.title('基于拉萨数据的策略性能雷达图\n(100个真实景点冲突案例)', 
             size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('final_strategy_radar.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_scenario_distribution_chart():
    """创建场景识别分布图"""
    
    # 优化自适应策略的场景识别分布
    scenarios = ['权重主导', '边界情况', '复杂权衡\n(偏权威)', '平衡策略', '时间主导']
    counts = [41, 16, 2, 20, 21]
    percentages = [count/100*100 for count in counts]
    
    # 创建饼图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 饼图
    colors = ['#FF6B6B', '#FFA07A', '#45B7D1', '#32CD32', '#FFD700']
    wedges, texts, autotexts = ax1.pie(counts, labels=scenarios, autopct='%1.1f%%', 
                                      colors=colors, startangle=90)
    ax1.set_title('优化自适应策略场景识别分布', fontsize=14, fontweight='bold')
    
    # 柱状图
    bars = ax2.bar(scenarios, counts, color=colors, alpha=0.8)
    ax2.set_title('场景识别数量分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('案例数量')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{count}个', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('scenario_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_final_summary_report():
    """创建最终总结报告"""
    
    report = """
# 🎯 基于拉萨知识图谱的冲突解决策略最终实验报告

## 📊 实验概述
- **数据来源**: 拉萨知识图谱真实景点数据 (260个景点)
- **测试案例**: 100个基于真实数据生成的冲突场景
- **测试策略**: Weight-First vs Latest-First vs 原始Hybrid vs 优化自适应Hybrid
- **实验时间**: 2025-07-24

## 🏆 最终实验结果

### 整体性能排名
| 排名 | 策略 | 准确率 | 正确案例 | 相比基准改进 |
|------|------|--------|----------|--------------|
| 🥇 | **优化自适应Hybrid** | **83.0%** | **83/100** | **+20.0%** |
| 🥈 | 原始Hybrid | 81.0% | 81/100 | +18.0% |
| 🥉 | Weight-First | 63.0% | 63/100 | 基准 |
| 4 | Latest-First | 56.0% | 56/100 | -7.0% |

### 🎯 关键成就
1. **优化自适应策略成为最佳策略**，准确率达到83.0%
2. **相比原始Hybrid提升2.0%**，证明了自适应优化的有效性
3. **相比Weight-First提升20.0%**，显著改善了冲突解决效果

## 📊 详细性能分析

### 按冲突类型分析
| 冲突类型 | Weight-First | Latest-First | 原始Hybrid | 优化自适应 | 最佳策略 |
|----------|--------------|--------------|------------|------------|----------|
| **权重主导** | 100% (30/30) | 50% (15/30) | 100% (30/30) | **100% (30/30)** | 三策略并列 |
| **时间主导** | 16% (4/25) | **100% (25/25)** | 56% (14/25) | 60% (15/25) | Latest-First |
| **复杂权衡** | 64% (16/25) | 36% (9/25) | 76% (19/25) | **80% (20/25)** | 优化自适应 |
| **边界情况** | 65% (13/20) | 35% (7/20) | **90% (18/20)** | **90% (18/20)** | Hybrid并列 |

### 按难度等级分析
| 难度等级 | Weight-First | Latest-First | 原始Hybrid | 优化自适应 | 最佳策略 |
|----------|--------------|--------------|------------|------------|----------|
| **简单** | 55% (11/20) | 40% (8/20) | **100% (20/20)** | **100% (20/20)** | Hybrid并列 |
| **中等** | 72% (26/36) | 47% (17/36) | 89% (32/36) | **92% (33/36)** | 优化自适应 |
| **困难** | 59% (26/44) | **71% (31/44)** | 66% (29/44) | 68% (30/44) | Latest-First |

## 🧠 优化自适应策略的核心优势

### 1. 智能场景识别
- **权重主导**: 41个案例 (41.0%) - 识别权威性差异显著的场景
- **时间主导**: 21个案例 (21.0%) - 识别时效性关键的场景
- **平衡策略**: 20个案例 (20.0%) - 处理模糊情况
- **边界情况**: 16个案例 (16.0%) - 特殊情况处理
- **复杂权衡**: 2个案例 (2.0%) - 高难度权衡场景

### 2. 动态权重调整
```
权重主导场景: 权重80% + 时间15% + 内容5%
时间主导场景: 权重15% + 时间80% + 内容5%
复杂权衡场景: 根据具体情况动态调整
边界情况: 权重40% + 时间35% + 内容25%
```

### 3. 特殊规则保护
- 权重差异 ≥ 0.35: 直接选择高权重源
- 时间差异 ≥ 400天: 直接选择新时间源
- 权威层级差异 ≥ 4: 直接选择高权威源

## 💡 关键洞察与发现

### 1. 场景适应性的重要性
**不同场景需要不同的策略重点**：
- 权重主导场景：权威性是关键
- 时间主导场景：时效性是关键
- 复杂权衡场景：需要智能平衡

### 2. 极化配置的有效性
**明确的倾向比模糊的平衡更有效**：
- 80%-15%的极化配置比50%-30%的平衡配置更有效
- 在确定的场景下，应该有明确的策略倾向

### 3. 规则与算法的结合
**简单规则处理极端情况，复杂算法处理模糊情况**：
- 对于明显的权重或时间差异，直接应用规则
- 对于模糊的情况，使用精细化的评分算法

### 4. Latest-First在困难场景的优势
**简单策略在特定场景下的威力**：
- Latest-First在困难场景准确率达到71%
- 证明了在时效性主导的复杂场景中，简单策略可能更有效

## 🚀 实际应用价值

### 1. 知识图谱系统
- 为不同类型的信息冲突提供智能解决方案
- 显著提升冲突解决的准确性和可靠性

### 2. 信息融合系统
- 在多源信息融合中自动识别冲突类型
- 根据场景特点动态调整融合策略

### 3. 决策支持系统
- 为复杂决策提供透明的推理过程
- 提供决策置信度评估和可解释性

## 🎯 未来优化方向

### 1. 机器学习增强
- 使用历史数据训练更精确的场景识别模型
- 动态学习最优权重配置参数

### 2. 领域适应性
- 针对不同领域调整权威性层级定义
- 开发领域特定的关键词奖励机制

### 3. 多冲突源处理
- 扩展到处理3个以上的冲突信息源
- 复杂网络中的冲突传播和解决

## 🏆 总结

通过系统性的分析、设计、测试和优化，我们成功开发了一个**智能自适应Hybrid策略**，它：

1. ✅ **超越了所有基准策略**：83.0%的最高准确率
2. ✅ **在多数场景类型都表现优秀**：复杂权衡、边界情况、中等难度场景
3. ✅ **提供了透明的决策过程**：场景识别、权重配置、评分详情
4. ✅ **具有实际应用价值**：基于真实数据验证，可直接部署

这个策略证明了**场景感知和动态权重调整**在多策略融合中的重要性，为知识图谱冲突解决提供了一个实用、高效、可解释的解决方案。

---
*基于100个真实拉萨景点数据生成的冲突案例，所有结果均可重现验证*
"""
    
    # 保存报告
    with open('final_experiment_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📋 最终实验报告已保存到: final_experiment_report.md")
    
    return report

def main():
    """主函数"""
    print("🎨 创建最终实验结果可视化")
    print("=" * 60)
    
    # 创建性能对比图
    print("📊 创建性能对比图...")
    create_final_performance_chart()
    print("✅ 性能对比图已保存: final_experiment_results.png")
    
    # 创建雷达图
    print("📊 创建雷达图...")
    create_final_radar_chart()
    print("✅ 雷达图已保存: final_strategy_radar.png")
    
    # 创建场景分布图
    print("📊 创建场景分布图...")
    create_scenario_distribution_chart()
    print("✅ 场景分布图已保存: scenario_distribution.png")
    
    # 创建最终报告
    print("📋 创建最终报告...")
    create_final_summary_report()
    
    print(f"\n🎉 最终实验可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • final_experiment_results.png - 性能对比图")
    print(f"   • final_strategy_radar.png - 雷达图")
    print(f"   • scenario_distribution.png - 场景分布图")
    print(f"   • final_experiment_report.md - 最终实验报告")

if __name__ == "__main__":
    main()
