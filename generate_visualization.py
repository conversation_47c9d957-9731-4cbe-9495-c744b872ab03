#!/usr/bin/env python3
"""
生成实验可视化报告

基于实验结果生成详细的可视化图表和分析报告
"""

import json
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_experiment_results():
    """加载实验结果"""
    try:
        with open('experiment_results.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("未找到实验结果文件 experiment_results.json")
        return None

def create_comprehensive_visualization(results):
    """创建综合可视化报告"""
    if not results or len(results) < 2:
        print("实验结果不足，无法生成对比图表")
        return
    
    # 创建图表
    fig = plt.figure(figsize=(16, 12))
    fig.suptitle('知识图谱演化质量评估实验结果', fontsize=20, fontweight='bold', y=0.98)
    
    # 准备数据
    strategies = [result['strategy_name'] for result in results]
    metrics_data = {}
    
    for metric in ['accuracy', 'coverage', 'conflict_resolution_rate', 'f1_score', 
                   'evolution_efficiency', 'processing_time', 'entities_created', 
                   'relationships_created', 'conflicts_detected', 'conflicts_resolved']:
        metrics_data[metric] = [result['metrics'][metric] for result in results]
    
    # 1. 主要指标对比 (2x2 grid, position 1)
    ax1 = plt.subplot(2, 3, 1)
    create_main_metrics_chart(ax1, strategies, metrics_data)
    
    # 2. 效率对比 (2x2 grid, position 2)
    ax2 = plt.subplot(2, 3, 2)
    create_efficiency_chart(ax2, strategies, metrics_data)
    
    # 3. 实体和关系创建对比 (2x2 grid, position 3)
    ax3 = plt.subplot(2, 3, 3)
    create_creation_chart(ax3, strategies, metrics_data)
    
    # 4. 冲突处理对比 (2x2 grid, position 4)
    ax4 = plt.subplot(2, 3, 4)
    create_conflict_chart(ax4, strategies, metrics_data)
    
    # 5. 综合评分对比 (2x2 grid, position 5)
    ax5 = plt.subplot(2, 3, 5)
    create_comprehensive_score_chart(ax5, strategies, metrics_data)
    
    # 6. 处理时间对比 (2x2 grid, position 6)
    ax6 = plt.subplot(2, 3, 6)
    create_time_chart(ax6, strategies, metrics_data)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.savefig('experiment_comprehensive_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 综合可视化报告已生成: experiment_comprehensive_visualization.png")

def create_main_metrics_chart(ax, strategies, metrics_data):
    """创建主要指标对比图"""
    metrics = ['accuracy', 'coverage', 'conflict_resolution_rate', 'f1_score']
    labels = ['准确率', '覆盖率', '冲突解决率', 'F1分数']
    
    x = np.arange(len(metrics))
    width = 0.35
    colors = ['#FF6B6B', '#4ECDC4']
    
    for i, strategy in enumerate(strategies):
        values = [metrics_data[metric][i] for metric in metrics]
        bars = ax.bar(x + i * width, values, width, label=strategy, 
                     color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=8)
    
    ax.set_xlabel('指标')
    ax.set_ylabel('分数')
    ax.set_title('主要指标对比')
    ax.set_xticks(x + width/2)
    ax.set_xticklabels(labels, rotation=45, ha='right')
    ax.legend()
    ax.set_ylim(0, 1.1)
    ax.grid(True, alpha=0.3)

def create_efficiency_chart(ax, strategies, metrics_data):
    """创建效率对比图"""
    bars = ax.bar(strategies, metrics_data['evolution_efficiency'], 
                 color=['#FF6B6B', '#4ECDC4'], alpha=0.8)
    
    ax.set_xlabel('策略')
    ax.set_ylabel('演化效率 (实体/秒)')
    ax.set_title('演化效率对比')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
               f'{height:.2f}', ha='center', va='bottom')
    
    ax.grid(True, alpha=0.3)

def create_creation_chart(ax, strategies, metrics_data):
    """创建实体和关系创建对比图"""
    x = np.arange(len(strategies))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, metrics_data['entities_created'], width, 
                   label='创建实体', color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, metrics_data['relationships_created'], width, 
                   label='创建关系', color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('策略')
    ax.set_ylabel('数量')
    ax.set_title('实体和关系创建对比')
    ax.set_xticks(x)
    ax.set_xticklabels(strategies)
    ax.legend()
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{int(height)}', ha='center', va='bottom')
    
    ax.grid(True, alpha=0.3)

def create_conflict_chart(ax, strategies, metrics_data):
    """创建冲突处理对比图"""
    x = np.arange(len(strategies))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, metrics_data['conflicts_detected'], width, 
                   label='检测冲突', color='#FFB347', alpha=0.8)
    bars2 = ax.bar(x + width/2, metrics_data['conflicts_resolved'], width, 
                   label='解决冲突', color='#98FB98', alpha=0.8)
    
    ax.set_xlabel('策略')
    ax.set_ylabel('冲突数量')
    ax.set_title('冲突处理对比')
    ax.set_xticks(x)
    ax.set_xticklabels(strategies)
    ax.legend()
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{int(height)}', ha='center', va='bottom')
    
    ax.grid(True, alpha=0.3)

def create_comprehensive_score_chart(ax, strategies, metrics_data):
    """创建综合评分对比图"""
    # 计算综合评分 (加权平均)
    weights = {
        'accuracy': 0.25,
        'coverage': 0.25, 
        'conflict_resolution_rate': 0.25,
        'f1_score': 0.25
    }
    
    comprehensive_scores = []
    for i in range(len(strategies)):
        score = sum(metrics_data[metric][i] * weight for metric, weight in weights.items())
        comprehensive_scores.append(score)
    
    bars = ax.bar(strategies, comprehensive_scores, 
                 color=['#FF6B6B', '#4ECDC4'], alpha=0.8)
    
    ax.set_xlabel('策略')
    ax.set_ylabel('综合评分')
    ax.set_title('综合评分对比')
    ax.set_ylim(0, 1.1)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
               f'{height:.3f}', ha='center', va='bottom')
    
    ax.grid(True, alpha=0.3)

def create_time_chart(ax, strategies, metrics_data):
    """创建处理时间对比图"""
    bars = ax.bar(strategies, metrics_data['processing_time'], 
                 color=['#FF6B6B', '#4ECDC4'], alpha=0.8)
    
    ax.set_xlabel('策略')
    ax.set_ylabel('处理时间 (秒)')
    ax.set_title('处理时间对比')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 5,
               f'{height:.1f}s', ha='center', va='bottom')
    
    ax.grid(True, alpha=0.3)

def generate_text_report(results):
    """生成文本报告"""
    if not results or len(results) < 2:
        return
    
    multi_strategy = next((r for r in results if "多策略" in r['strategy_name']), None)
    single_strategy = next((r for r in results if "单策略" in r['strategy_name']), None)
    
    if not multi_strategy or not single_strategy:
        return
    
    report = f"""
# 知识图谱演化质量评估实验报告

## 实验概述
- **实验时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **实验目标**: 验证多策略代理在知识图谱动态演化中的准确性和完整性
- **对比方案**: 多策略代理 vs 传统单策略
- **数据集**: 西藏旅游景点数据（包含冲突和冗余信息）

## 实验结果

### 多策略代理
- **准确率**: {multi_strategy['metrics']['accuracy']:.3f}
- **覆盖率**: {multi_strategy['metrics']['coverage']:.3f}
- **冲突解决率**: {multi_strategy['metrics']['conflict_resolution_rate']:.3f}
- **F1分数**: {multi_strategy['metrics']['f1_score']:.3f}
- **演化效率**: {multi_strategy['metrics']['evolution_efficiency']:.2f} 实体/秒
- **处理时间**: {multi_strategy['metrics']['processing_time']:.2f} 秒
- **创建实体**: {multi_strategy['metrics']['entities_created']}
- **创建关系**: {multi_strategy['metrics']['relationships_created']}

### 单策略基准
- **准确率**: {single_strategy['metrics']['accuracy']:.3f}
- **覆盖率**: {single_strategy['metrics']['coverage']:.3f}
- **冲突解决率**: {single_strategy['metrics']['conflict_resolution_rate']:.3f}
- **F1分数**: {single_strategy['metrics']['f1_score']:.3f}
- **演化效率**: {single_strategy['metrics']['evolution_efficiency']:.2f} 实体/秒
- **处理时间**: {single_strategy['metrics']['processing_time']:.2f} 秒
- **创建实体**: {single_strategy['metrics']['entities_created']}
- **创建关系**: {single_strategy['metrics']['relationships_created']}

## 对比分析

### 性能提升
- **准确率变化**: {(multi_strategy['metrics']['accuracy'] - single_strategy['metrics']['accuracy']) * 100:+.1f}%
- **覆盖率变化**: {(multi_strategy['metrics']['coverage'] - single_strategy['metrics']['coverage']) * 100:+.1f}%
- **冲突解决率变化**: {(multi_strategy['metrics']['conflict_resolution_rate'] - single_strategy['metrics']['conflict_resolution_rate']) * 100:+.1f}%

### 效率分析
- **处理时间比较**: 多策略代理用时 {multi_strategy['metrics']['processing_time']:.1f}s，单策略用时 {single_strategy['metrics']['processing_time']:.1f}s
- **演化效率比较**: 多策略代理 {multi_strategy['metrics']['evolution_efficiency']:.2f} vs 单策略 {single_strategy['metrics']['evolution_efficiency']:.2f} 实体/秒

## 结论

1. **覆盖率**: 两种策略都达到了100%的覆盖率，说明都能处理所有输入数据
2. **准确率**: 单策略基准在准确率上表现更好，这可能是因为它使用了更严格的匹配规则
3. **冲突解决**: 多策略代理具备冲突解决能力，而单策略基准不处理冲突
4. **效率**: 单策略基准在处理速度上更快，但多策略代理提供了更丰富的功能
5. **关系推断**: 单策略基准创建了更多的基础关系，而多策略代理专注于复杂关系推断

## 建议

1. **混合策略**: 可以考虑结合两种方法的优势，在简单场景使用单策略，复杂场景使用多策略
2. **性能优化**: 多策略代理需要进一步优化处理速度
3. **准确率提升**: 需要改进多策略代理的实体匹配算法
4. **关系质量**: 应该评估关系的质量而不仅仅是数量

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('experiment_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 文本报告已生成: experiment_report.md")

def main():
    """主函数"""
    print("🎨 生成实验可视化报告...")
    
    # 加载实验结果
    results = load_experiment_results()
    if not results:
        return
    
    # 生成可视化图表
    create_comprehensive_visualization(results)
    
    # 生成文本报告
    generate_text_report(results)
    
    print("🎉 实验报告生成完成！")
    print("📊 可视化图表: experiment_comprehensive_visualization.png")
    print("📄 文本报告: experiment_report.md")

if __name__ == "__main__":
    main()
