#!/usr/bin/env python3
"""
仅使用Gemini的验证器

当只有Gemini API可用时，使用多次查询来模拟多模型验证
"""

import os
import json
import re
import time
from typing import List, Dict, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('config.env')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GeminiJudgment:
    """Gemini判断结果"""
    query_id: int
    choice: str  # 'A' 或 'B'
    confidence: float  # 0-1之间
    reasoning: str
    response_time: float
    success: bool

class GeminiOnlyValidator:
    """仅使用Gemini的验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.setup_client()
        self.judgment_prompts = self.create_judgment_prompts()
        
    def setup_client(self):
        """设置Gemini客户端"""
        try:
            api_key = os.getenv('GOOGLE_API_KEY')
            if not api_key:
                raise ValueError("未找到GOOGLE_API_KEY环境变量")
            
            genai.configure(api_key=api_key)
            model_name = os.getenv('GOOGLE_MODEL', 'gemini-2.0-flash-exp')
            self.model = genai.GenerativeModel(model_name)
            
            logger.info(f"✅ Gemini客户端初始化成功 (模型: {model_name})")
            
        except Exception as e:
            logger.error(f"❌ Gemini客户端初始化失败: {e}")
            raise
    
    def create_judgment_prompts(self) -> List[str]:
        """创建多个不同角度的判断提示词"""
        
        prompts = [
            # 提示词1：权威性优先视角
            """
你是一位权威性评估专家。请分析以下两个冲突的信息源，从权威性角度选择更可靠的一个。

## 冲突信息：
{conflict_description}

## 评估重点：
1. **权威性** (主要考虑)
   - 信息源的官方程度和专业性
   - 发布机构的历史可信度
   - 是否有权威认证

2. **时效性** (次要考虑)
   - 信息的新旧程度

3. **内容质量** (辅助考虑)
   - 信息的详细程度

## 请按以下格式回答：
**选择**: A 或 B
**置信度**: 1-10分 (10分最确信)
**理由**: 详细说明选择原因，重点分析权威性
""",
            
            # 提示词2：时效性优先视角
            """
你是一位信息时效性专家。请分析以下两个冲突的信息源，从时效性角度选择更可靠的一个。

## 冲突信息：
{conflict_description}

## 评估重点：
1. **时效性** (主要考虑)
   - 信息的新旧程度
   - 是否反映最新情况
   - 在该领域的时间敏感度

2. **权威性** (次要考虑)
   - 信息源的可信度

3. **内容质量** (辅助考虑)
   - 信息的完整性

## 请按以下格式回答：
**选择**: A 或 B
**置信度**: 1-10分 (10分最确信)
**理由**: 详细说明选择原因，重点分析时效性
""",
            
            # 提示词3：综合平衡视角
            """
你是一位信息质量综合评估专家。请分析以下两个冲突的信息源，综合考虑各个因素选择更可靠的一个。

## 冲突信息：
{conflict_description}

## 评估标准（平衡考虑）：
1. **权威性** (35%权重)
   - 信息源的官方程度和专业性

2. **时效性** (35%权重)  
   - 信息的新旧程度和时间敏感度

3. **内容质量** (30%权重)
   - 信息的详细程度和完整性

## 请按以下格式回答：
**选择**: A 或 B
**置信度**: 1-10分 (10分最确信)
**理由**: 详细说明选择原因，平衡分析各个因素
"""
        ]
        
        return prompts
    
    def format_conflict_for_judgment(self, conflicts: List[Dict]) -> str:
        """格式化冲突信息供模型判断"""
        if len(conflicts) != 2:
            raise ValueError("只支持两个冲突源的对比")
        
        conflict_a, conflict_b = conflicts
        
        def extract_info(conflict, label):
            source = conflict.get('source', {})
            return f"""
**信息源{label}**:
- 实体: {conflict.get('entity_name', 'N/A')}
- 属性: {conflict.get('attribute', 'N/A')}
- 内容: {conflict.get('value', 'N/A')}
- 来源类型: {source.get('type', 'N/A')}
- 权重: {source.get('weight', 'N/A')}
- 时间: {conflict.get('timestamp', 'N/A')}
"""
        
        return extract_info(conflict_a, 'A') + extract_info(conflict_b, 'B')
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def query_gemini_with_prompt(self, conflict_description: str, prompt_template: str, query_id: int) -> GeminiJudgment:
        """使用特定提示词查询Gemini"""
        start_time = time.time()
        
        try:
            prompt = prompt_template.format(conflict_description=conflict_description)
            
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,  # 低温度保证一致性
                    max_output_tokens=800,
                )
            )
            
            response_text = response.text
            choice, confidence, reasoning = self.parse_model_response(response_text)
            
            return GeminiJudgment(
                query_id=query_id,
                choice=choice,
                confidence=confidence,
                reasoning=reasoning,
                response_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Gemini查询{query_id}失败: {e}")
            return GeminiJudgment(
                query_id=query_id,
                choice="",
                confidence=0.0,
                reasoning=f"查询失败: {str(e)}",
                response_time=time.time() - start_time,
                success=False
            )
    
    def parse_model_response(self, response_text: str) -> Tuple[str, float, str]:
        """解析模型响应"""
        try:
            # 提取选择 (A或B)
            choice_match = re.search(r'\*\*选择\*\*[：:]\s*([AB])', response_text, re.IGNORECASE)
            if not choice_match:
                choice_match = re.search(r'选择[：:]?\s*([AB])', response_text, re.IGNORECASE)
            
            choice = choice_match.group(1).upper() if choice_match else ""
            
            # 提取置信度 (1-10分)
            confidence_match = re.search(r'\*\*置信度\*\*[：:]\s*(\d+)', response_text, re.IGNORECASE)
            if not confidence_match:
                confidence_match = re.search(r'置信度[：:]?\s*(\d+)', response_text, re.IGNORECASE)
            
            confidence_score = int(confidence_match.group(1)) if confidence_match else 5
            confidence = min(confidence_score / 10.0, 1.0)  # 转换为0-1范围
            
            # 提取理由
            reasoning_match = re.search(r'\*\*理由\*\*[：:]\s*(.*)', response_text, re.IGNORECASE | re.DOTALL)
            if not reasoning_match:
                reasoning_match = re.search(r'理由[：:]?\s*(.*)', response_text, re.IGNORECASE | re.DOTALL)
            
            reasoning = reasoning_match.group(1).strip() if reasoning_match else response_text
            
            return choice, confidence, reasoning
            
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return "", 0.5, response_text
    
    def get_multi_perspective_judgment(self, conflicts: List[Dict]) -> Dict:
        """获取多视角判断"""
        
        logger.info(f"🔄 开始多视角Gemini验证...")
        
        # 格式化冲突描述
        conflict_description = self.format_conflict_for_judgment(conflicts)
        
        # 收集多个视角的判断
        judgments = []
        
        for i, prompt_template in enumerate(self.judgment_prompts):
            logger.info(f"📞 查询Gemini视角{i+1}...")
            
            judgment = self.query_gemini_with_prompt(conflict_description, prompt_template, i+1)
            judgments.append(judgment)
            
            # 添加延迟避免API限制
            if i < len(self.judgment_prompts) - 1:
                time.sleep(2)
        
        # 分析一致性
        consensus = self.analyze_multi_perspective_consensus(judgments)
        
        logger.info(f"✅ 多视角验证完成，共识选择: {consensus['final_choice']}, 一致率: {consensus['agreement_rate']:.2f}")
        
        return consensus
    
    def analyze_multi_perspective_consensus(self, judgments: List[GeminiJudgment]) -> Dict:
        """分析多视角共识"""
        
        # 只考虑成功的判断
        successful_judgments = [j for j in judgments if j.success and j.choice in ['A', 'B']]
        
        if not successful_judgments:
            return {
                'final_choice': "",
                'agreement_rate': 0.0,
                'confidence': 0.0,
                'individual_judgments': judgments,
                'reasoning_summary': "所有查询都失败了"
            }
        
        # 统计选择
        choice_counts = {'A': 0, 'B': 0}
        total_confidence = {'A': 0.0, 'B': 0.0}
        
        for judgment in successful_judgments:
            choice_counts[judgment.choice] += 1
            total_confidence[judgment.choice] += judgment.confidence
        
        # 确定多数选择
        if choice_counts['A'] > choice_counts['B']:
            majority_choice = 'A'
        elif choice_counts['B'] > choice_counts['A']:
            majority_choice = 'B'
        else:
            # 平局时选择置信度更高的
            avg_conf_a = total_confidence['A'] / max(choice_counts['A'], 1)
            avg_conf_b = total_confidence['B'] / max(choice_counts['B'], 1)
            majority_choice = 'A' if avg_conf_a >= avg_conf_b else 'B'
        
        # 计算一致率
        agreement_count = choice_counts[majority_choice]
        agreement_rate = agreement_count / len(successful_judgments)
        
        # 计算综合置信度
        majority_confidences = [j.confidence for j in successful_judgments if j.choice == majority_choice]
        avg_confidence = sum(majority_confidences) / len(majority_confidences) if majority_confidences else 0.5
        
        # 一致性加权置信度
        final_confidence = avg_confidence * agreement_rate
        
        # 生成推理摘要
        reasoning_summary = self.generate_reasoning_summary(successful_judgments, majority_choice)
        
        return {
            'final_choice': majority_choice,
            'agreement_rate': agreement_rate,
            'confidence': final_confidence,
            'individual_judgments': judgments,
            'reasoning_summary': reasoning_summary
        }
    
    def generate_reasoning_summary(self, judgments: List[GeminiJudgment], majority_choice: str) -> str:
        """生成推理摘要"""
        
        majority_reasons = []
        minority_reasons = []
        
        for judgment in judgments:
            if judgment.choice == majority_choice:
                majority_reasons.append(f"视角{judgment.query_id}: {judgment.reasoning[:150]}...")
            else:
                minority_reasons.append(f"视角{judgment.query_id}: {judgment.reasoning[:150]}...")
        
        summary = f"多数视角选择 {majority_choice}:\n"
        summary += "\n".join(majority_reasons)
        
        if minority_reasons:
            summary += f"\n\n少数视角的不同观点:\n"
            summary += "\n".join(minority_reasons)
        
        return summary

def main():
    """测试函数"""
    
    # 示例冲突案例
    test_conflicts = [
        {
            "entity_name": "布达拉宫",
            "attribute": "描述",
            "value": "世界文化遗产，藏传佛教圣地（官方权威认证，详细介绍包含历史文化背景）",
            "source": {
                "type": "government",
                "url": "http://government.gov/potala-palace",
                "weight": 0.95
            },
            "timestamp": "2024-03-10T10:00:00Z"
        },
        {
            "entity_name": "布达拉宫",
            "attribute": "描述", 
            "value": "听说布达拉宫还行吧，没去过",
            "source": {
                "type": "personal_blog",
                "url": "http://personal_blog.com/post123",
                "weight": 0.25
            },
            "timestamp": "2024-03-12T10:00:00Z"
        }
    ]
    
    # 创建验证器
    validator = GeminiOnlyValidator()
    
    # 获取多视角判断
    result = validator.get_multi_perspective_judgment(test_conflicts)
    
    # 输出结果
    print(f"\n🎯 Gemini多视角验证结果:")
    print(f"最终选择: {result['final_choice']}")
    print(f"一致率: {result['agreement_rate']:.2f}")
    print(f"置信度: {result['confidence']:.2f}")
    print(f"\n📝 推理摘要:\n{result['reasoning_summary']}")
    
    print(f"\n📊 各视角详细结果:")
    for judgment in result['individual_judgments']:
        status = "✅" if judgment.success else "❌"
        print(f"{status} 视角{judgment.query_id}: {judgment.choice} (置信度: {judgment.confidence:.2f})")

if __name__ == "__main__":
    main()
