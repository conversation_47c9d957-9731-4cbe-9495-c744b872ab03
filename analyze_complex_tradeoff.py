#!/usr/bin/env python3
"""
分析复杂权衡场景的问题

检查为什么Hybrid和Weight-First在复杂权衡场景表现相同
"""

import json
import re
from datetime import datetime
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    try:
        with open('test_dataset_100_complete.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 测试数据集文件未找到")
        return []

def parse_timestamp(timestamp_str):
    """解析时间戳"""
    timestamp_str = timestamp_str.replace('Z', '+00:00')
    timestamp_str = re.sub(r'T(\d):(\d\d):(\d\d)', r'T0\1:\2:\3', timestamp_str)
    return datetime.fromisoformat(timestamp_str)

def resolve_conflict_weight_first(conflicts):
    """权重优先策略"""
    return max(conflicts, key=lambda x: x["source"]["weight"])

def resolve_conflict_latest_first(conflicts):
    """时间优先策略"""
    return max(conflicts, key=lambda x: parse_timestamp(x["timestamp"]))

def resolve_conflict_hybrid(conflicts):
    """混合策略"""
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict["source"]["weight"]
        timestamp = parse_timestamp(conflict["timestamp"])
        
        # 时间新鲜度评分
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)  # 2年内线性衰减
        
        # 内容质量评分
        content_length = len(conflict["value"])
        content_score = min(1.0, content_length / 100)
        
        # 综合评分
        final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def analyze_complex_tradeoff_cases():
    """分析复杂权衡场景"""
    
    print("🔍 复杂权衡场景详细分析")
    print("="*80)
    
    test_cases = load_test_dataset()
    if not test_cases:
        return
    
    # 筛选复杂权衡场景
    complex_cases = [case for case in test_cases if case['conflict_type'] == 'complex_tradeoff']
    
    print(f"📊 复杂权衡场景总数: {len(complex_cases)}")
    
    # 分析每个复杂权衡案例
    strategies = {
        "weight_first": resolve_conflict_weight_first,
        "latest_first": resolve_conflict_latest_first,
        "hybrid": resolve_conflict_hybrid
    }
    
    detailed_analysis = []
    
    for i, case in enumerate(complex_cases[:10]):  # 分析前10个案例
        print(f"\n📋 案例 {i+1}: {case['name']}")
        print("-" * 60)
        
        conflicts = case['conflicts']
        expected_winner = case['expected_winner_id']
        
        # 显示冲突信息
        for j, conflict in enumerate(conflicts):
            timestamp = parse_timestamp(conflict['timestamp'])
            print(f"   选项{j+1}: 权重={conflict['source']['weight']:.2f}, "
                  f"时间={timestamp.strftime('%Y-%m-%d')}, "
                  f"源类型={conflict['source']['type']}")
        
        print(f"   预期获胜者: {expected_winner}")
        
        # 测试三种策略
        case_results = {}
        for strategy_name, strategy_func in strategies.items():
            chosen = strategy_func(conflicts)
            chosen_url = chosen['source']['url']
            is_correct = chosen_url == expected_winner
            
            case_results[strategy_name] = {
                'chosen_url': chosen_url,
                'is_correct': is_correct,
                'chosen_weight': chosen['source']['weight'],
                'chosen_timestamp': chosen['timestamp']
            }
            
            print(f"   {strategy_name}: {'✅' if is_correct else '❌'} "
                  f"选择权重={chosen['source']['weight']:.2f}")
        
        detailed_analysis.append({
            'case': case,
            'results': case_results
        })
    
    return detailed_analysis

def analyze_why_same_performance():
    """分析为什么Hybrid和Weight-First表现相同"""
    
    print(f"\n🔍 为什么Hybrid和Weight-First在复杂权衡场景表现相同？")
    print("="*80)
    
    test_cases = load_test_dataset()
    complex_cases = [case for case in test_cases if case['conflict_type'] == 'complex_tradeoff']
    
    strategies = {
        "weight_first": resolve_conflict_weight_first,
        "hybrid": resolve_conflict_hybrid
    }
    
    same_choice_count = 0
    different_choice_count = 0
    
    for case in complex_cases:
        conflicts = case['conflicts']
        
        weight_choice = resolve_conflict_weight_first(conflicts)
        hybrid_choice = resolve_conflict_hybrid(conflicts)
        
        if weight_choice['source']['url'] == hybrid_choice['source']['url']:
            same_choice_count += 1
        else:
            different_choice_count += 1
    
    print(f"📊 选择一致性分析:")
    print(f"   相同选择: {same_choice_count}/{len(complex_cases)} ({same_choice_count/len(complex_cases):.1%})")
    print(f"   不同选择: {different_choice_count}/{len(complex_cases)} ({different_choice_count/len(complex_cases):.1%})")
    
    if same_choice_count > different_choice_count:
        print(f"\n💡 原因分析:")
        print("   在复杂权衡场景中，权重因素仍然占主导地位")
        print("   Hybrid策略的权重占比(50%)使其倾向于选择高权重选项")
        print("   时间和内容质量因素(30%+20%)不足以逆转权重优势")

def analyze_scenario_classification():
    """分析场景分类的合理性"""
    
    print(f"\n🔍 场景分类分析")
    print("="*80)
    
    test_cases = load_test_dataset()
    
    # 按类型分组
    type_groups = defaultdict(list)
    for case in test_cases:
        type_groups[case['conflict_type']].append(case)
    
    print("📊 各场景类型的特征分析:")
    
    for conflict_type, cases in type_groups.items():
        print(f"\n🏷️  {conflict_type} ({len(cases)}个案例):")
        
        # 分析权重差异
        weight_diffs = []
        time_diffs = []
        
        for case in cases[:5]:  # 分析前5个案例
            conflicts = case['conflicts']
            if len(conflicts) == 2:
                weight1 = conflicts[0]['source']['weight']
                weight2 = conflicts[1]['source']['weight']
                weight_diff = abs(weight1 - weight2)
                weight_diffs.append(weight_diff)
                
                time1 = parse_timestamp(conflicts[0]['timestamp'])
                time2 = parse_timestamp(conflicts[1]['timestamp'])
                time_diff = abs((time1 - time2).days)
                time_diffs.append(time_diff)
        
        if weight_diffs and time_diffs:
            avg_weight_diff = sum(weight_diffs) / len(weight_diffs)
            avg_time_diff = sum(time_diffs) / len(time_diffs)
            
            print(f"   平均权重差异: {avg_weight_diff:.3f}")
            print(f"   平均时间差异: {avg_time_diff:.0f}天")
            
            # 分类特征
            if conflict_type == "weight_dominant":
                print("   特征: 权重差异大(>0.4)，时间差异可变")
            elif conflict_type == "time_dominant":
                print("   特征: 权重差异小(<0.2)，时间差异大(>500天)")
            elif conflict_type == "complex_tradeoff":
                print("   特征: 权重差异中等(0.2-0.4)，时间差异中等")
            elif conflict_type == "edge_case":
                print("   特征: 权重和时间都非常接近")

def suggest_improvements():
    """建议改进方案"""
    
    print(f"\n🚀 改进建议")
    print("="*80)
    
    print("📈 问题诊断:")
    print("   1. 复杂权衡场景的设计可能不够'复杂'")
    print("   2. Hybrid策略的权重占比(50%)过高，导致仍然偏向权重")
    print("   3. 场景分类的边界可能需要调整")
    
    print(f"\n🔧 解决方案:")
    print("   1. 调整Hybrid策略的权重分配:")
    print("      - 权重: 40% (降低)")
    print("      - 时间: 35% (提高)")
    print("      - 内容: 25% (提高)")
    
    print("   2. 重新设计复杂权衡场景:")
    print("      - 权重差异: 0.15-0.25 (更小的差异)")
    print("      - 时间差异: 300-800天 (中等差异)")
    print("      - 增加内容质量差异作为决定因素")
    
    print("   3. 增加新的场景类型:")
    print("      - 内容质量主导型")
    print("      - 多因素平衡型")
    print("      - 动态权重调整型")

def main():
    """主函数"""
    
    print("🔍 复杂权衡场景问题分析")
    print("="*80)
    print("问题: 为什么Hybrid和Weight-First在复杂权衡场景表现相同？")
    print("="*80)
    
    # 1. 详细分析复杂权衡案例
    detailed_analysis = analyze_complex_tradeoff_cases()
    
    # 2. 分析相同表现的原因
    analyze_why_same_performance()
    
    # 3. 分析场景分类
    analyze_scenario_classification()
    
    # 4. 提出改进建议
    suggest_improvements()
    
    print(f"\n🎯 总结")
    print("="*60)
    print("✅ 识别了复杂权衡场景设计的问题")
    print("✅ 分析了Hybrid策略权重分配的影响")
    print("✅ 提出了具体的改进方案")
    print("✅ 为后续优化提供了方向")

if __name__ == "__main__":
    main()
