#!/usr/bin/env python3
"""
测试改进的Hybrid策略

基于对失败案例的分析，设计更智能的混合策略
"""

import json
from datetime import datetime
from typing import List, Dict
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

def resolve_conflict_original_hybrid(conflicts: List[Dict]) -> Dict:
    """原始混合策略"""
    if not conflicts:
        return {}
    
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict.get('source', {}).get('weight', 0)
        timestamp = parse_timestamp(conflict.get('timestamp', ''))
        content_length = len(conflict.get('value', ''))
        
        # 时间新鲜度评分 (2年内线性衰减)
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)
        
        # 内容质量评分
        content_score = min(1.0, content_length / 100)
        
        # 综合评分: 权重50% + 时间30% + 内容20%
        final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def resolve_conflict_improved_hybrid_v1(conflicts: List[Dict]) -> Dict:
    """改进版本1: 动态权重调整"""
    if not conflicts or len(conflicts) != 2:
        return conflicts[0] if conflicts else {}
    
    c1, c2 = conflicts
    
    # 计算时间差
    time1 = parse_timestamp(c1['timestamp'])
    time2 = parse_timestamp(c2['timestamp'])
    time_diff_days = abs((time1 - time2).days)
    
    # 计算权重差
    weight1 = c1['source']['weight']
    weight2 = c2['source']['weight']
    weight_diff = abs(weight1 - weight2)
    
    # 动态调整权重比例
    if time_diff_days > 300:  # 时间差很大，偏向时效性
        weight_ratio, time_ratio = 0.3, 0.6
    elif time_diff_days > 120:  # 时间差中等，平衡
        weight_ratio, time_ratio = 0.4, 0.5
    else:  # 时间差较小，偏向权威性
        weight_ratio, time_ratio = 0.6, 0.3
    
    content_ratio = 1 - weight_ratio - time_ratio
    
    # 计算评分
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict['source']['weight']
        timestamp = parse_timestamp(conflict['timestamp'])
        content_length = len(conflict['value'])
        
        # 时间评分
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)
        
        # 内容评分
        content_score = min(1.0, content_length / 100)
        
        # 动态权重评分
        final_score = weight * weight_ratio + time_score * time_ratio + content_score * content_ratio
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def resolve_conflict_improved_hybrid_v2(conflicts: List[Dict]) -> Dict:
    """改进版本2: 阈值决策机制"""
    if not conflicts or len(conflicts) != 2:
        return conflicts[0] if conflicts else {}
    
    c1, c2 = conflicts
    
    # 计算基本指标
    weight1, weight2 = c1['source']['weight'], c2['source']['weight']
    weight_diff = abs(weight1 - weight2)
    
    time1 = parse_timestamp(c1['timestamp'])
    time2 = parse_timestamp(c2['timestamp'])
    time_diff_days = abs((time1 - time2).days)
    
    # 阈值决策
    if weight_diff > 0.25:  # 权威性差异显著
        return max(conflicts, key=lambda x: x['source']['weight'])
    elif time_diff_days > 200:  # 时间差异显著
        return max(conflicts, key=lambda x: parse_timestamp(x['timestamp']))
    else:
        # 使用平衡策略
        best_conflict = None
        best_score = -1
        
        for conflict in conflicts:
            weight = conflict['source']['weight']
            timestamp = parse_timestamp(conflict['timestamp'])
            content_length = len(conflict['value'])
            
            # 时间评分
            now = datetime.now(timestamp.tzinfo)
            days_old = (now - timestamp).days
            time_score = max(0, 1 - days_old / 730)
            
            # 内容评分
            content_score = min(1.0, content_length / 100)
            
            # 平衡评分
            final_score = weight * 0.45 + time_score * 0.45 + content_score * 0.1
            
            if final_score > best_score:
                best_score = final_score
                best_conflict = conflict
        
        return best_conflict

def resolve_conflict_improved_hybrid_v3(conflicts: List[Dict]) -> Dict:
    """改进版本3: 场景感知策略"""
    if not conflicts or len(conflicts) != 2:
        return conflicts[0] if conflicts else {}
    
    c1, c2 = conflicts
    
    # 计算指标
    weight1, weight2 = c1['source']['weight'], c2['source']['weight']
    weight_diff = abs(weight1 - weight2)
    
    time1 = parse_timestamp(c1['timestamp'])
    time2 = parse_timestamp(c2['timestamp'])
    time_diff_days = abs((time1 - time2).days)
    
    # 场景识别
    if weight_diff > 0.2 and time_diff_days < 100:
        # 权重主导场景
        weight_ratio, time_ratio = 0.7, 0.2
    elif time_diff_days > 250 and weight_diff < 0.15:
        # 时间主导场景
        weight_ratio, time_ratio = 0.2, 0.7
    elif time_diff_days > 180 and weight_diff > 0.1:
        # 复杂权衡场景 - 偏向时效性
        weight_ratio, time_ratio = 0.35, 0.55
    else:
        # 边界情况 - 平衡策略
        weight_ratio, time_ratio = 0.5, 0.4
    
    content_ratio = 1 - weight_ratio - time_ratio
    
    # 计算评分
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict['source']['weight']
        timestamp = parse_timestamp(conflict['timestamp'])
        content_length = len(conflict['value'])
        
        # 时间评分
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)
        
        # 内容评分
        content_score = min(1.0, content_length / 100)
        
        # 场景感知评分
        final_score = weight * weight_ratio + time_score * time_ratio + content_score * content_ratio
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def test_strategy(test_cases, strategy_func, strategy_name):
    """测试单个策略"""
    correct_count = 0
    results_by_type = defaultdict(lambda: {'correct': 0, 'total': 0})
    results_by_difficulty = defaultdict(lambda: {'correct': 0, 'total': 0})
    
    for case in test_cases:
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        conflict_type = case['conflict_type']
        difficulty = case['difficulty']
        
        # 应用策略
        winner = strategy_func(conflicts)
        winner_id = winner.get('source', {}).get('url', '') if winner else ''
        
        is_correct = winner_id == expected_id
        if is_correct:
            correct_count += 1
        
        # 按类型统计
        results_by_type[conflict_type]['total'] += 1
        if is_correct:
            results_by_type[conflict_type]['correct'] += 1
        
        # 按难度统计
        results_by_difficulty[difficulty]['total'] += 1
        if is_correct:
            results_by_difficulty[difficulty]['correct'] += 1
    
    accuracy = correct_count / len(test_cases) if test_cases else 0
    
    return {
        'strategy': strategy_name,
        'accuracy': accuracy,
        'correct_count': correct_count,
        'total_count': len(test_cases),
        'by_type': dict(results_by_type),
        'by_difficulty': dict(results_by_difficulty)
    }

def compare_strategies():
    """对比所有策略"""
    test_cases = load_test_dataset()
    
    strategies = [
        (resolve_conflict_original_hybrid, "原始Hybrid"),
        (resolve_conflict_improved_hybrid_v1, "改进v1-动态权重"),
        (resolve_conflict_improved_hybrid_v2, "改进v2-阈值决策"),
        (resolve_conflict_improved_hybrid_v3, "改进v3-场景感知")
    ]
    
    print("🔬 改进Hybrid策略对比测试")
    print("=" * 80)
    
    all_results = []
    
    for strategy_func, strategy_name in strategies:
        result = test_strategy(test_cases, strategy_func, strategy_name)
        all_results.append(result)
        
        print(f"\n📊 {strategy_name}")
        print(f"   整体准确率: {result['accuracy']:.3f} ({result['correct_count']}/{result['total_count']})")
        
        # 按难度分析
        print(f"   按难度分析:")
        for difficulty in ['easy', 'medium', 'hard']:
            if difficulty in result['by_difficulty']:
                stats = result['by_difficulty'][difficulty]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                print(f"     {difficulty}: {acc:.3f} ({stats['correct']}/{stats['total']})")
        
        # 按类型分析
        print(f"   按类型分析:")
        for conflict_type in ['weight_dominant', 'time_dominant', 'complex_tradeoff', 'edge_case']:
            if conflict_type in result['by_type']:
                stats = result['by_type'][conflict_type]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                print(f"     {conflict_type}: {acc:.3f} ({stats['correct']}/{stats['total']})")
    
    # 找出最佳策略
    best_overall = max(all_results, key=lambda x: x['accuracy'])
    print(f"\n🏆 最佳整体策略: {best_overall['strategy']} (准确率: {best_overall['accuracy']:.3f})")
    
    # 困难场景最佳策略
    best_hard = max(all_results, 
                   key=lambda x: x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / 
                                x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total'])
    hard_acc = best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / \
               best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total']
    print(f"🎯 困难场景最佳: {best_hard['strategy']} (准确率: {hard_acc:.3f})")
    
    return all_results

def analyze_improvements():
    """分析改进效果"""
    print(f"\n💡 改进策略设计思路分析")
    print("=" * 60)
    
    print("🔧 改进v1 - 动态权重调整:")
    print("   • 根据时间差动态调整权重和时间的比例")
    print("   • 时间差 > 300天: 时间权重60%, 权重权重30%")
    print("   • 时间差 < 120天: 权重权重60%, 时间权重30%")
    print("   • 优势: 能够根据具体情况调整策略重点")
    
    print("\n🎯 改进v2 - 阈值决策机制:")
    print("   • 权重差异 > 0.25: 直接选择权威性高的")
    print("   • 时间差异 > 200天: 直接选择时间新的")
    print("   • 否则使用平衡策略")
    print("   • 优势: 在明显差异时避免复杂计算")
    
    print("\n🧠 改进v3 - 场景感知策略:")
    print("   • 识别不同的冲突场景类型")
    print("   • 为每种场景设计专门的权重分配")
    print("   • 权重主导: 权重70%, 时间20%")
    print("   • 时间主导: 时间70%, 权重20%")
    print("   • 优势: 最接近人类专家的判断逻辑")

def main():
    """主函数"""
    print("🚀 改进Hybrid策略测试与分析")
    print("=" * 80)
    
    # 对比测试所有策略
    results = compare_strategies()
    
    # 分析改进思路
    analyze_improvements()
    
    print(f"\n🎯 核心发现:")
    print(f"=" * 60)
    print(f"1. 原始Hybrid策略的固定权重分配是主要问题")
    print(f"2. 动态调整或阈值决策能显著改善性能")
    print(f"3. 场景感知策略最接近人类专家判断")
    print(f"4. 在困难场景下，智能策略比简单策略更有效")

if __name__ == "__main__":
    main()
