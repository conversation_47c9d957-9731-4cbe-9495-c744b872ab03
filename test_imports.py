#!/usr/bin/env python3
"""
测试导入脚本
"""

print("开始测试导入...")

try:
    import aiohttp
    print("✓ aiohttp 导入成功")
except ImportError as e:
    print(f"✗ aiohttp 导入失败: {e}")

try:
    import httpx
    print("✓ httpx 导入成功")
except ImportError as e:
    print(f"✗ httpx 导入失败: {e}")

try:
    import neo4j
    print("✓ neo4j 导入成功")
except ImportError as e:
    print(f"✗ neo4j 导入失败: {e}")

try:
    import langchain
    print("✓ langchain 导入成功")
except ImportError as e:
    print(f"✗ langchain 导入失败: {e}")

try:
    from sentence_transformers import SentenceTransformer
    print("✓ sentence_transformers 导入成功")
except ImportError as e:
    print(f"✗ sentence_transformers 导入失败: {e}")

print("\n开始测试项目模块...")

try:
    import config
    print("✓ config 导入成功")
except ImportError as e:
    print(f"✗ config 导入失败: {e}")

try:
    import knowledge_graph_updater
    print("✓ knowledge_graph_updater 导入成功")
except ImportError as e:
    print(f"✗ knowledge_graph_updater 导入失败: {e}")

try:
    import text_processor
    print("✓ text_processor 导入成功")
except ImportError as e:
    print(f"✗ text_processor 导入失败: {e}")

print("导入测试完成！")
