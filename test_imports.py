#!/usr/bin/env python3
"""
测试项目依赖和模块导入
"""

import sys
import traceback

def test_external_dependencies():
    """测试外部依赖"""
    print("🔍 测试外部依赖...")
    
    dependencies = [
        ('neo4j', 'Neo4j数据库驱动'),
        ('langchain', 'Lang<PERSON>hain框架'),
        ('sentence_transformers', '句子转换器'),
        ('matplotlib', '绘图库'),
        ('pandas', '数据处理'),
        ('numpy', '数值计算'),
        ('httpx', 'HTTP客户端'),
        ('aiohttp', '异步HTTP客户端'),
        ('tenacity', '重试机制'),
        ('openai', 'OpenAI客户端'),
        ('anthropic', 'Anthropic客户端'),
        ('google.generativeai', 'Google生成式AI'),
    ]
    
    failed = []
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
        except ImportError as e:
            print(f"  ❌ {module} - {description}: {e}")
            failed.append(module)
    
    return len(failed) == 0

def test_project_modules():
    """测试项目模块"""
    print("\n🔍 测试项目模块...")
    
    modules = [
        ('config', 'Config', '配置管理'),
        ('neo4j_connection', 'Neo4jConnection', 'Neo4j连接'),
        ('neo4j_crud', 'Neo4jCRUD', 'Neo4j CRUD操作'),
        ('utils', 'compute_semantic_similarity', '语义相似度计算'),
        ('utils', 'normalize_location', '位置标准化'),
        ('conflict_resolution', 'ConflictResolver', '冲突解决'),
        ('enhanced_conflict_resolution', 'EnhancedConflictResolver', '增强冲突解决'),
        ('enhanced_conflict_resolution', 'EntityDisambiguation', '实体消歧'),
        ('knowledge_graph_updater', 'KnowledgeGraphUpdater', '知识图谱更新'),
        ('text_processor', 'process_json_chunk', '文本处理'),
        ('time_converter', 'convert_to_beijing_time', '时间转换'),
    ]
    
    failed = []
    for module_name, class_or_func, description in modules:
        try:
            module = __import__(module_name, fromlist=[class_or_func])
            getattr(module, class_or_func)
            print(f"  ✅ {module_name}.{class_or_func} - {description}")
        except (ImportError, AttributeError) as e:
            print(f"  ❌ {module_name}.{class_or_func} - {description}: {e}")
            failed.append(f"{module_name}.{class_or_func}")
    
    return len(failed) == 0

def test_config():
    """测试配置"""
    print("\n🔍 测试配置...")
    
    try:
        from config import Config
        
        # 测试配置获取
        neo4j_config = Config.get_neo4j_config()
        print(f"  ✅ Neo4j配置: {neo4j_config['uri']}")
        
        api_key = Config.get_deepseek_api_key()
        print(f"  ✅ API密钥: {'已设置' if api_key else '未设置'}")
        
        llm_config = Config.get_llm_config()
        print(f"  ✅ LLM配置: {llm_config['model']}")
        
        return True
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试项目依赖和模块...")
    
    results = []
    
    # 测试外部依赖
    results.append(test_external_dependencies())
    
    # 测试项目模块
    results.append(test_project_modules())
    
    # 测试配置
    results.append(test_config())
    
    # 总结
    print("\n" + "="*50)
    if all(results):
        print("🎉 所有测试通过！项目依赖和模块都正常工作。")
        print("\n📋 下一步:")
        print("  1. 确保Neo4j数据库正在运行")
        print("  2. 检查API密钥配置")
        print("  3. 运行主程序: python langchain_neo4j_test.py")
        return 0
    else:
        print("❌ 部分测试失败，请检查上述错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())