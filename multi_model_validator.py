#!/usr/bin/env python3
"""
多AI模型交叉验证器

使用GPT-4、<PERSON>和<PERSON>对冲突案例进行独立判断，
通过一致性分析提供更客观的"正确答案"
"""

import os
import json
import re
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

# 第三方库
import openai
import anthropic
import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('config.env')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelJudgment:
    """模型判断结果"""
    model_name: str
    choice: str  # 'A' 或 'B'
    confidence: float  # 0-1之间
    reasoning: str
    response_time: float
    success: bool

@dataclass
class ConsensusResult:
    """共识结果"""
    final_choice: str
    agreement_rate: float
    confidence: float
    individual_judgments: List[ModelJudgment]
    reasoning_summary: str

class MultiModelValidator:
    """多模型交叉验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.setup_clients()
        self.judgment_prompt = self.create_judgment_prompt()
        
    def setup_clients(self):
        """设置API客户端"""
        try:
            # OpenAI客户端
            openai.api_key = os.getenv('OPENAI_API_KEY')
            self.openai_client = openai.OpenAI()
            
            # Anthropic客户端
            self.anthropic_client = anthropic.Anthropic(
                api_key=os.getenv('ANTHROPIC_API_KEY')
            )
            
            # Google客户端
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
            model_name = os.getenv('GOOGLE_MODEL', 'gemini-2.0-flash-exp')
            self.google_model = genai.GenerativeModel(model_name)
            
            logger.info("✅ 所有API客户端初始化成功")
            
        except Exception as e:
            logger.error(f"❌ API客户端初始化失败: {e}")
            raise
    
    def create_judgment_prompt(self) -> str:
        """创建判断提示词"""
        return """
你是一位信息质量评估专家。请分析以下两个冲突的信息源，选择更可靠的一个。

## 冲突信息：
{conflict_description}

## 评估标准：
1. **权威性** (40%权重)
   - 信息源的官方程度和专业性
   - 发布机构的历史可信度
   - 是否有权威认证

2. **时效性** (35%权重)  
   - 信息的新旧程度
   - 是否反映最新情况
   - 在该领域的时间敏感度

3. **内容质量** (25%权重)
   - 信息的详细程度和完整性
   - 逻辑一致性和可验证性
   - 是否包含具体数据

## 请按以下格式回答：
**选择**: A 或 B
**置信度**: 1-10分 (10分最确信)
**理由**: 详细说明选择原因，包括权威性、时效性、内容质量的分析

请确保回答格式严格按照上述要求。
"""
    
    def format_conflict_for_judgment(self, conflicts: List[Dict]) -> str:
        """格式化冲突信息供模型判断"""
        if len(conflicts) != 2:
            raise ValueError("只支持两个冲突源的对比")
        
        conflict_a, conflict_b = conflicts
        
        # 提取关键信息
        def extract_info(conflict, label):
            source = conflict.get('source', {})
            return f"""
**信息源{label}**:
- 实体: {conflict.get('entity_name', 'N/A')}
- 属性: {conflict.get('attribute', 'N/A')}
- 内容: {conflict.get('value', 'N/A')}
- 来源类型: {source.get('type', 'N/A')}
- 权重: {source.get('weight', 'N/A')}
- 时间: {conflict.get('timestamp', 'N/A')}
- URL: {source.get('url', 'N/A')}
"""
        
        return extract_info(conflict_a, 'A') + extract_info(conflict_b, 'B')
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def query_gpt4(self, conflict_description: str) -> ModelJudgment:
        """查询GPT-4"""
        start_time = time.time()
        
        try:
            prompt = self.judgment_prompt.format(conflict_description=conflict_description)
            
            response = self.openai_client.chat.completions.create(
                model=os.getenv('OPENAI_MODEL', 'gpt-4'),
                messages=[
                    {"role": "system", "content": "你是一位专业的信息质量评估专家。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            response_text = response.choices[0].message.content
            choice, confidence, reasoning = self.parse_model_response(response_text)
            
            return ModelJudgment(
                model_name="GPT-4",
                choice=choice,
                confidence=confidence,
                reasoning=reasoning,
                response_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"GPT-4查询失败: {e}")
            return ModelJudgment(
                model_name="GPT-4",
                choice="",
                confidence=0.0,
                reasoning=f"查询失败: {str(e)}",
                response_time=time.time() - start_time,
                success=False
            )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def query_claude(self, conflict_description: str) -> ModelJudgment:
        """查询Claude"""
        start_time = time.time()
        
        try:
            prompt = self.judgment_prompt.format(conflict_description=conflict_description)
            
            response = self.anthropic_client.messages.create(
                model=os.getenv('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229'),
                max_tokens=1000,
                temperature=0.1,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            response_text = response.content[0].text
            choice, confidence, reasoning = self.parse_model_response(response_text)
            
            return ModelJudgment(
                model_name="Claude",
                choice=choice,
                confidence=confidence,
                reasoning=reasoning,
                response_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Claude查询失败: {e}")
            return ModelJudgment(
                model_name="Claude",
                choice="",
                confidence=0.0,
                reasoning=f"查询失败: {str(e)}",
                response_time=time.time() - start_time,
                success=False
            )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def query_gemini(self, conflict_description: str) -> ModelJudgment:
        """查询Gemini"""
        start_time = time.time()
        
        try:
            prompt = self.judgment_prompt.format(conflict_description=conflict_description)
            
            response = self.google_model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=1000,
                )
            )
            
            response_text = response.text
            choice, confidence, reasoning = self.parse_model_response(response_text)
            
            return ModelJudgment(
                model_name="Gemini",
                choice=choice,
                confidence=confidence,
                reasoning=reasoning,
                response_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Gemini查询失败: {e}")
            return ModelJudgment(
                model_name="Gemini",
                choice="",
                confidence=0.0,
                reasoning=f"查询失败: {str(e)}",
                response_time=time.time() - start_time,
                success=False
            )
    
    def parse_model_response(self, response_text: str) -> Tuple[str, float, str]:
        """解析模型响应"""
        try:
            # 提取选择 (A或B)
            choice_match = re.search(r'\*\*选择\*\*[：:]\s*([AB])', response_text, re.IGNORECASE)
            if not choice_match:
                choice_match = re.search(r'选择[：:]?\s*([AB])', response_text, re.IGNORECASE)
            
            choice = choice_match.group(1).upper() if choice_match else ""
            
            # 提取置信度 (1-10分)
            confidence_match = re.search(r'\*\*置信度\*\*[：:]\s*(\d+)', response_text, re.IGNORECASE)
            if not confidence_match:
                confidence_match = re.search(r'置信度[：:]?\s*(\d+)', response_text, re.IGNORECASE)
            
            confidence_score = int(confidence_match.group(1)) if confidence_match else 5
            confidence = min(confidence_score / 10.0, 1.0)  # 转换为0-1范围
            
            # 提取理由
            reasoning_match = re.search(r'\*\*理由\*\*[：:]\s*(.*)', response_text, re.IGNORECASE | re.DOTALL)
            if not reasoning_match:
                reasoning_match = re.search(r'理由[：:]?\s*(.*)', response_text, re.IGNORECASE | re.DOTALL)
            
            reasoning = reasoning_match.group(1).strip() if reasoning_match else response_text
            
            return choice, confidence, reasoning
            
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return "", 0.5, response_text
    
    def get_consensus_judgment(self, conflicts: List[Dict]) -> ConsensusResult:
        """获取多模型共识判断"""
        
        logger.info(f"🔄 开始多模型验证...")
        
        # 格式化冲突描述
        conflict_description = self.format_conflict_for_judgment(conflicts)
        
        # 收集各模型判断
        judgments = []
        
        # 查询GPT-4
        logger.info("📞 查询GPT-4...")
        gpt4_judgment = self.query_gpt4(conflict_description)
        judgments.append(gpt4_judgment)
        
        # 查询Claude
        logger.info("📞 查询Claude...")
        claude_judgment = self.query_claude(conflict_description)
        judgments.append(claude_judgment)
        
        # 查询Gemini
        logger.info("📞 查询Gemini...")
        gemini_judgment = self.query_gemini(conflict_description)
        judgments.append(gemini_judgment)
        
        # 分析共识
        consensus = self.analyze_consensus(judgments)
        
        logger.info(f"✅ 多模型验证完成，共识选择: {consensus.final_choice}, 一致率: {consensus.agreement_rate:.2f}")
        
        return consensus
    
    def analyze_consensus(self, judgments: List[ModelJudgment]) -> ConsensusResult:
        """分析多模型共识"""
        
        # 只考虑成功的判断
        successful_judgments = [j for j in judgments if j.success and j.choice in ['A', 'B']]
        
        if not successful_judgments:
            return ConsensusResult(
                final_choice="",
                agreement_rate=0.0,
                confidence=0.0,
                individual_judgments=judgments,
                reasoning_summary="所有模型查询都失败了"
            )
        
        # 统计选择
        choice_counts = {'A': 0, 'B': 0}
        total_confidence = {'A': 0.0, 'B': 0.0}
        
        for judgment in successful_judgments:
            choice_counts[judgment.choice] += 1
            total_confidence[judgment.choice] += judgment.confidence
        
        # 确定多数选择
        if choice_counts['A'] > choice_counts['B']:
            majority_choice = 'A'
        elif choice_counts['B'] > choice_counts['A']:
            majority_choice = 'B'
        else:
            # 平局时选择置信度更高的
            avg_conf_a = total_confidence['A'] / max(choice_counts['A'], 1)
            avg_conf_b = total_confidence['B'] / max(choice_counts['B'], 1)
            majority_choice = 'A' if avg_conf_a >= avg_conf_b else 'B'
        
        # 计算一致率
        agreement_count = choice_counts[majority_choice]
        agreement_rate = agreement_count / len(successful_judgments)
        
        # 计算综合置信度
        majority_confidences = [j.confidence for j in successful_judgments if j.choice == majority_choice]
        avg_confidence = sum(majority_confidences) / len(majority_confidences) if majority_confidences else 0.5
        
        # 一致性加权置信度
        final_confidence = avg_confidence * agreement_rate
        
        # 生成推理摘要
        reasoning_summary = self.generate_reasoning_summary(successful_judgments, majority_choice)
        
        return ConsensusResult(
            final_choice=majority_choice,
            agreement_rate=agreement_rate,
            confidence=final_confidence,
            individual_judgments=judgments,
            reasoning_summary=reasoning_summary
        )
    
    def generate_reasoning_summary(self, judgments: List[ModelJudgment], majority_choice: str) -> str:
        """生成推理摘要"""
        
        majority_reasons = []
        minority_reasons = []
        
        for judgment in judgments:
            if judgment.choice == majority_choice:
                majority_reasons.append(f"{judgment.model_name}: {judgment.reasoning[:200]}...")
            else:
                minority_reasons.append(f"{judgment.model_name}: {judgment.reasoning[:200]}...")
        
        summary = f"多数模型选择 {majority_choice}:\n"
        summary += "\n".join(majority_reasons)
        
        if minority_reasons:
            summary += f"\n\n少数模型的不同观点:\n"
            summary += "\n".join(minority_reasons)
        
        return summary

def main():
    """测试函数"""
    
    # 示例冲突案例
    test_conflicts = [
        {
            "entity_name": "布达拉宫",
            "attribute": "描述",
            "value": "世界文化遗产，藏传佛教圣地（官方权威认证，详细介绍包含历史文化背景）",
            "source": {
                "type": "government",
                "url": "http://government.gov/potala-palace",
                "weight": 0.95
            },
            "timestamp": "2024-03-10T10:00:00Z"
        },
        {
            "entity_name": "布达拉宫",
            "attribute": "描述", 
            "value": "听说布达拉宫还行吧，没去过",
            "source": {
                "type": "personal_blog",
                "url": "http://personal_blog.com/post123",
                "weight": 0.25
            },
            "timestamp": "2024-03-12T10:00:00Z"
        }
    ]
    
    # 创建验证器
    validator = MultiModelValidator()
    
    # 获取共识判断
    result = validator.get_consensus_judgment(test_conflicts)
    
    # 输出结果
    print(f"\n🎯 多模型验证结果:")
    print(f"最终选择: {result.final_choice}")
    print(f"一致率: {result.agreement_rate:.2f}")
    print(f"置信度: {result.confidence:.2f}")
    print(f"\n📝 推理摘要:\n{result.reasoning_summary}")
    
    print(f"\n📊 各模型详细结果:")
    for judgment in result.individual_judgments:
        status = "✅" if judgment.success else "❌"
        print(f"{status} {judgment.model_name}: {judgment.choice} (置信度: {judgment.confidence:.2f})")

if __name__ == "__main__":
    main()
