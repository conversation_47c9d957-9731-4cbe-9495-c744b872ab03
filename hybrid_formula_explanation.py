#!/usr/bin/env python3
"""
Hybrid策略公式详细解释

通过具体例子说明 final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
"""

from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_hybrid_score_detailed(conflict, current_time=None):
    """详细计算Hybrid评分，显示每一步"""
    
    if current_time is None:
        current_time = datetime.now()
    
    print(f"📊 Hybrid策略评分计算详解")
    print("="*60)
    
    # 1. 权重分数 (直接使用)
    weight = conflict["source"]["weight"]
    print(f"1️⃣ 权重分数 (Weight Score):")
    print(f"   原始权重: {weight}")
    print(f"   权重分数: {weight} (直接使用)")
    
    # 2. 时间新鲜度分数
    timestamp_str = conflict["timestamp"].replace('Z', '+00:00')
    timestamp = datetime.fromisoformat(timestamp_str)

    # 确保时间对象都有时区信息
    if current_time.tzinfo is None:
        current_time = current_time.replace(tzinfo=timestamp.tzinfo)

    days_old = (current_time - timestamp).days
    time_score = max(0, 1 - days_old / 730)  # 730天 = 2年
    
    print(f"\n2️⃣ 时间新鲜度分数 (Time Score):")
    print(f"   信息时间: {timestamp.strftime('%Y-%m-%d')}")
    print(f"   距今天数: {days_old}天")
    print(f"   计算公式: max(0, 1 - {days_old}/730)")
    print(f"   时间分数: {time_score:.3f}")
    
    # 3. 内容质量分数
    content_length = len(conflict["value"])
    content_score = min(1.0, content_length / 100)
    
    print(f"\n3️⃣ 内容质量分数 (Content Score):")
    print(f"   内容长度: {content_length}字符")
    print(f"   计算公式: min(1.0, {content_length}/100)")
    print(f"   内容分数: {content_score:.3f}")
    
    # 4. 综合评分
    final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
    
    print(f"\n4️⃣ 综合评分 (Final Score):")
    print(f"   公式: weight×0.5 + time_score×0.3 + content_score×0.2")
    print(f"   计算: {weight}×0.5 + {time_score:.3f}×0.3 + {content_score:.3f}×0.2")
    print(f"   详细: {weight*0.5:.3f} + {time_score*0.3:.3f} + {content_score*0.2:.3f}")
    print(f"   最终得分: {final_score:.3f}")
    
    return final_score

def compare_two_conflicts():
    """对比两个冲突信息的评分"""
    
    print(f"\n🔍 冲突对比示例")
    print("="*80)
    
    # 冲突信息A：高权重但旧信息
    conflict_a = {
        "entity_name": "布达拉宫",
        "attribute": "门票价格", 
        "value": "门票价格：200元/人，学生票100元/人，需要提前预约，旺季可能涨价",  # 长内容
        "source": {
            "type": "government",
            "url": "http://gov.cn/palace",
            "weight": 0.95  # 高权重
        },
        "timestamp": "2022-01-15T10:00:00Z"  # 旧信息
    }
    
    # 冲突信息B：低权重但新信息
    conflict_b = {
        "entity_name": "布达拉宫",
        "attribute": "门票价格",
        "value": "门票涨价了，现在要250元",  # 短内容
        "source": {
            "type": "personal_blog", 
            "url": "http://myblog.com/tibet",
            "weight": 0.25  # 低权重
        },
        "timestamp": "2023-12-01T15:30:00Z"  # 新信息
    }
    
    current_time = datetime(2024, 1, 1)  # 假设当前时间
    
    print("🅰️ 冲突信息A (政府源，旧信息):")
    print(f"   来源: {conflict_a['source']['type']} (权重: {conflict_a['source']['weight']})")
    print(f"   时间: {conflict_a['timestamp']}")
    print(f"   内容: {conflict_a['value']}")
    
    score_a = calculate_hybrid_score_detailed(conflict_a, current_time)
    
    print(f"\n🅱️ 冲突信息B (个人博客，新信息):")
    print(f"   来源: {conflict_b['source']['type']} (权重: {conflict_b['source']['weight']})")
    print(f"   时间: {conflict_b['timestamp']}")
    print(f"   内容: {conflict_b['value']}")
    
    score_b = calculate_hybrid_score_detailed(conflict_b, current_time)
    
    print(f"\n🏆 最终决策:")
    print("="*40)
    if score_a > score_b:
        print(f"✅ 选择信息A (得分: {score_a:.3f} > {score_b:.3f})")
        print("   原因: 高权重优势超过了时间劣势")
    else:
        print(f"✅ 选择信息B (得分: {score_b:.3f} > {score_a:.3f})")
        print("   原因: 时间和内容优势超过了权重劣势")

def analyze_weight_distribution():
    """分析权重分配的影响"""
    
    print(f"\n📊 权重分配影响分析")
    print("="*60)
    
    print("当前权重分配:")
    print("   权重 (Weight): 50% - 主导因素")
    print("   时间 (Time): 30% - 重要因素") 
    print("   内容 (Content): 20% - 辅助因素")
    
    print(f"\n💡 这意味着:")
    print("   • 权重差异 > 0.4 时，权重几乎总是获胜")
    print("   • 权重差异 0.2-0.4 时，时间和内容可能影响结果")
    print("   • 权重差异 < 0.2 时，时间和内容成为决定因素")
    
    # 临界点分析
    print(f"\n🎯 临界点分析:")
    print("假设时间分数=1.0, 内容分数=1.0 (最优)")
    print("高权重源要获胜，需要:")
    print("   weight_high * 0.5 > weight_low * 0.5 + 1.0 * 0.3 + 1.0 * 0.2")
    print("   weight_high * 0.5 > weight_low * 0.5 + 0.5")
    print("   weight_high - weight_low > 1.0")
    print("   结论: 权重差异需要 > 1.0 才能被完全逆转")
    print("   实际上权重最大差异约0.8，所以权重通常占主导")

def demonstrate_different_scenarios():
    """演示不同场景下的评分"""
    
    print(f"\n🎭 不同场景演示")
    print("="*60)
    
    scenarios = [
        {
            "name": "权重主导场景",
            "conflict_a": {"weight": 0.9, "days_old": 500, "content_len": 80},
            "conflict_b": {"weight": 0.3, "days_old": 30, "content_len": 120}
        },
        {
            "name": "时间主导场景", 
            "conflict_a": {"weight": 0.65, "days_old": 800, "content_len": 90},
            "conflict_b": {"weight": 0.6, "days_old": 10, "content_len": 110}
        },
        {
            "name": "复杂权衡场景",
            "conflict_a": {"weight": 0.8, "days_old": 400, "content_len": 60},
            "conflict_b": {"weight": 0.7, "days_old": 50, "content_len": 140}
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        
        # 计算A的得分
        a = scenario['conflict_a']
        time_score_a = max(0, 1 - a['days_old'] / 730)
        content_score_a = min(1.0, a['content_len'] / 100)
        final_score_a = a['weight'] * 0.5 + time_score_a * 0.3 + content_score_a * 0.2
        
        # 计算B的得分
        b = scenario['conflict_b']
        time_score_b = max(0, 1 - b['days_old'] / 730)
        content_score_b = min(1.0, b['content_len'] / 100)
        final_score_b = b['weight'] * 0.5 + time_score_b * 0.3 + content_score_b * 0.2
        
        print(f"   选项A: 权重{a['weight']}, {a['days_old']}天前, {a['content_len']}字 → 得分: {final_score_a:.3f}")
        print(f"   选项B: 权重{b['weight']}, {b['days_old']}天前, {b['content_len']}字 → 得分: {final_score_b:.3f}")
        
        winner = "A" if final_score_a > final_score_b else "B"
        print(f"   🏆 获胜者: 选项{winner}")

def main():
    """主函数"""
    
    print("🎯 Hybrid策略公式详解")
    print("="*80)
    print("公式: final_score = weight × 0.5 + time_score × 0.3 + content_score × 0.2")
    print("="*80)
    
    # 1. 对比两个具体冲突
    compare_two_conflicts()
    
    # 2. 分析权重分配影响
    analyze_weight_distribution()
    
    # 3. 演示不同场景
    demonstrate_different_scenarios()
    
    print(f"\n🎉 总结")
    print("="*60)
    print("✅ Hybrid策略通过加权平均综合考虑三个因素")
    print("✅ 权重占50%，是最重要的决定因素")
    print("✅ 时间和内容共占50%，可以在权重相近时发挥作用")
    print("✅ 这解释了为什么在复杂权衡场景中仍然偏向高权重")

if __name__ == "__main__":
    main()
