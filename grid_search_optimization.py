#!/usr/bin/env python3
"""
网格搜索优化权重配置

系统性测试不同的权重配置，找到最优参数组合
"""

import json
import itertools
from datetime import datetime
from typing import List, Dict, Tuple
from collections import defaultdict
import numpy as np

def load_test_dataset():
    """加载测试数据集"""
    with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

class GridSearchOptimizer:
    """网格搜索优化器"""
    
    def __init__(self):
        # 当前基准配置
        self.baseline_config = {
            'weight_dominant': {'weight': 0.80, 'time': 0.15, 'content': 0.05},
            'time_dominant': {'weight': 0.15, 'time': 0.80, 'content': 0.05},
            'complex_tradeoff_favor_authority': {'weight': 0.65, 'time': 0.30, 'content': 0.05},
            'complex_tradeoff_favor_recency': {'weight': 0.25, 'time': 0.70, 'content': 0.05},
            'edge_case': {'weight': 0.40, 'time': 0.35, 'content': 0.25},
            'balanced': {'weight': 0.45, 'time': 0.45, 'content': 0.10}
        }
        
        # 场景识别阈值
        self.thresholds = {
            'weight_significant_threshold': 0.15,
            'time_significant_threshold': 120,
            'time_critical_threshold': 200
        }
    
    def generate_weight_candidates(self, scenario: str, step_size: float = 0.05) -> List[Dict]:
        """为特定场景生成权重候选配置"""
        
        candidates = []
        base_config = self.baseline_config[scenario]
        
        if scenario == 'weight_dominant':
            # 权重主导场景：主要调整权重和时间的比例
            weight_values = [0.70, 0.75, 0.80, 0.85, 0.90]
            for w in weight_values:
                remaining = 1.0 - w
                # 时间权重在剩余中占大部分，内容权重很小
                time_values = [0.08, 0.10, 0.12, 0.15, 0.18]
                for t in time_values:
                    if t <= remaining:
                        c = remaining - t
                        if c >= 0.02 and c <= 0.08:  # 内容权重合理范围
                            candidates.append({'weight': w, 'time': t, 'content': c})
        
        elif scenario == 'time_dominant':
            # 时间主导场景：主要调整时间和权重的比例
            time_values = [0.70, 0.75, 0.80, 0.85, 0.90]
            for t in time_values:
                remaining = 1.0 - t
                weight_values = [0.08, 0.10, 0.12, 0.15, 0.18]
                for w in weight_values:
                    if w <= remaining:
                        c = remaining - w
                        if c >= 0.02 and c <= 0.08:
                            candidates.append({'weight': w, 'time': t, 'content': c})
        
        elif scenario == 'edge_case':
            # 边界情况：内容权重较高，权重和时间相对平衡
            content_values = [0.20, 0.25, 0.30, 0.35]
            for c in content_values:
                remaining = 1.0 - c
                # 权重和时间相对平衡
                weight_ratios = [0.4, 0.45, 0.5, 0.55, 0.6]  # 在剩余中的比例
                for ratio in weight_ratios:
                    w = remaining * ratio
                    t = remaining * (1 - ratio)
                    candidates.append({'weight': w, 'time': t, 'content': c})
        
        elif scenario in ['complex_tradeoff_favor_authority', 'complex_tradeoff_favor_recency']:
            # 复杂权衡场景：在权威性和时效性之间调整
            if 'authority' in scenario:
                # 偏权威：权重占主导
                weight_values = [0.60, 0.65, 0.70, 0.75]
                for w in weight_values:
                    remaining = 1.0 - w
                    time_ratios = [0.7, 0.8, 0.9]  # 在剩余中的比例
                    for ratio in time_ratios:
                        t = remaining * ratio
                        c = remaining * (1 - ratio)
                        if c >= 0.02:
                            candidates.append({'weight': w, 'time': t, 'content': c})
            else:
                # 偏时效：时间占主导
                time_values = [0.65, 0.70, 0.75, 0.80]
                for t in time_values:
                    remaining = 1.0 - t
                    weight_ratios = [0.7, 0.8, 0.9]
                    for ratio in weight_ratios:
                        w = remaining * ratio
                        c = remaining * (1 - ratio)
                        if c >= 0.02:
                            candidates.append({'weight': w, 'time': t, 'content': c})
        
        elif scenario == 'balanced':
            # 平衡场景：权重和时间相近
            balance_values = [0.40, 0.42, 0.45, 0.47, 0.50]
            content_values = [0.08, 0.10, 0.12, 0.15]
            for c in content_values:
                remaining = 1.0 - c
                for balance in balance_values:
                    w = remaining * balance
                    t = remaining * (1 - balance)
                    candidates.append({'weight': w, 'time': t, 'content': c})
        
        # 去重并排序
        unique_candidates = []
        seen = set()
        for candidate in candidates:
            # 四舍五入到小数点后3位避免浮点误差
            rounded = tuple(round(v, 3) for v in candidate.values())
            if rounded not in seen:
                seen.add(rounded)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    def test_configuration(self, config: Dict[str, Dict]) -> Dict:
        """测试特定配置的性能"""
        
        test_cases = load_test_dataset()
        if not test_cases:
            return {'accuracy': 0.0, 'details': {}}
        
        # 创建临时的自适应解决器
        temp_resolver = TempAdaptiveResolver(config, self.thresholds)
        
        # 统计结果
        total_correct = 0
        scenario_stats = defaultdict(lambda: {'correct': 0, 'total': 0})
        
        for test_case in test_cases:
            # 执行策略
            chosen_conflict = temp_resolver.resolve_conflict(test_case['conflicts'])
            chosen_url = chosen_conflict.get('source', {}).get('url', '')
            expected_url = test_case['expected_winner_id']
            
            is_correct = chosen_url == expected_url
            if is_correct:
                total_correct += 1
            
            # 按场景统计
            scenario = temp_resolver.identify_scenario(test_case['conflicts'])
            scenario_stats[scenario]['total'] += 1
            if is_correct:
                scenario_stats[scenario]['correct'] += 1
        
        # 计算准确率
        overall_accuracy = total_correct / len(test_cases)
        
        # 计算各场景准确率
        scenario_accuracies = {}
        for scenario, stats in scenario_stats.items():
            if stats['total'] > 0:
                scenario_accuracies[scenario] = stats['correct'] / stats['total']
            else:
                scenario_accuracies[scenario] = 0.0
        
        return {
            'accuracy': overall_accuracy,
            'scenario_accuracies': scenario_accuracies,
            'scenario_stats': dict(scenario_stats)
        }
    
    def optimize_scenario(self, scenario: str) -> Tuple[Dict, float]:
        """优化特定场景的权重配置"""
        
        print(f"\n🔍 优化场景: {scenario}")
        print("=" * 50)
        
        # 生成候选配置
        candidates = self.generate_weight_candidates(scenario)
        print(f"📊 生成 {len(candidates)} 个候选配置")
        
        # 测试基准配置
        baseline_config = self.baseline_config.copy()
        baseline_result = self.test_configuration(baseline_config)
        baseline_accuracy = baseline_result['accuracy']
        
        print(f"📈 基准准确率: {baseline_accuracy:.4f}")
        
        # 测试所有候选配置
        best_config = None
        best_accuracy = baseline_accuracy
        best_result = baseline_result
        
        results = []
        
        for i, candidate in enumerate(candidates):
            # 创建测试配置
            test_config = baseline_config.copy()
            test_config[scenario] = candidate
            
            # 测试性能
            result = self.test_configuration(test_config)
            accuracy = result['accuracy']
            
            results.append({
                'config': candidate,
                'accuracy': accuracy,
                'improvement': accuracy - baseline_accuracy,
                'result': result
            })
            
            # 更新最佳配置
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_config = candidate
                best_result = result
            
            # 进度显示
            if (i + 1) % 10 == 0 or i == len(candidates) - 1:
                print(f"   进度: {i+1}/{len(candidates)}, 当前最佳: {best_accuracy:.4f}")
        
        # 排序结果
        results.sort(key=lambda x: x['accuracy'], reverse=True)
        
        # 显示结果
        print(f"\n📊 {scenario} 优化结果:")
        print(f"   最佳配置: {best_config}")
        print(f"   最佳准确率: {best_accuracy:.4f}")
        print(f"   改进幅度: {best_accuracy - baseline_accuracy:+.4f}")
        
        print(f"\n🏆 前5名配置:")
        for i, result in enumerate(results[:5]):
            config = result['config']
            accuracy = result['accuracy']
            improvement = result['improvement']
            print(f"   {i+1}. 权重:{config['weight']:.2f} 时间:{config['time']:.2f} 内容:{config['content']:.2f} "
                  f"准确率:{accuracy:.4f} 改进:{improvement:+.4f}")
        
        return best_config, best_accuracy, results
    
    def full_grid_search(self) -> Dict:
        """完整的网格搜索优化"""
        
        print("🚀 开始完整网格搜索优化")
        print("=" * 60)
        
        # 测试基准性能
        baseline_result = self.test_configuration(self.baseline_config)
        baseline_accuracy = baseline_result['accuracy']
        
        print(f"📈 基准整体准确率: {baseline_accuracy:.4f}")
        print(f"📊 基准各场景准确率:")
        for scenario, acc in baseline_result['scenario_accuracies'].items():
            print(f"   {scenario}: {acc:.4f}")
        
        # 逐场景优化
        optimized_config = self.baseline_config.copy()
        optimization_results = {}
        
        scenarios_to_optimize = [
            'weight_dominant',
            'time_dominant', 
            'edge_case',
            'complex_tradeoff_favor_authority',
            'complex_tradeoff_favor_recency',
            'balanced'
        ]
        
        for scenario in scenarios_to_optimize:
            best_config, best_accuracy, results = self.optimize_scenario(scenario)
            
            # 如果有改进，更新配置
            if best_accuracy > baseline_accuracy:
                optimized_config[scenario] = best_config
                optimization_results[scenario] = {
                    'best_config': best_config,
                    'improvement': best_accuracy - baseline_accuracy,
                    'all_results': results[:10]  # 保存前10名
                }
                print(f"✅ {scenario} 配置已更新")
            else:
                print(f"⚠️ {scenario} 无显著改进，保持原配置")
        
        # 测试最终优化配置
        final_result = self.test_configuration(optimized_config)
        final_accuracy = final_result['accuracy']
        
        print(f"\n🎉 优化完成!")
        print(f"📈 最终准确率: {final_accuracy:.4f}")
        print(f"📊 总体改进: {final_accuracy - baseline_accuracy:+.4f}")
        
        return {
            'baseline_accuracy': baseline_accuracy,
            'final_accuracy': final_accuracy,
            'improvement': final_accuracy - baseline_accuracy,
            'optimized_config': optimized_config,
            'optimization_results': optimization_results,
            'final_result': final_result
        }

class TempAdaptiveResolver:
    """临时的自适应解决器，用于测试不同配置"""
    
    def __init__(self, scenario_weights: Dict, thresholds: Dict):
        self.scenario_weights = scenario_weights
        self.weight_significant_threshold = thresholds['weight_significant_threshold']
        self.time_significant_threshold = thresholds['time_significant_threshold']
        self.time_critical_threshold = thresholds['time_critical_threshold']
    
    def analyze_conflict_characteristics(self, conflicts: List[Dict]) -> Dict:
        """分析冲突特征"""
        if len(conflicts) != 2:
            return {}
        
        c1, c2 = conflicts
        
        # 权重分析
        weight1, weight2 = c1['source']['weight'], c2['source']['weight']
        weight_diff = abs(weight1 - weight2)
        higher_weight_idx = 0 if weight1 > weight2 else 1
        
        # 时间分析
        time1 = parse_timestamp(c1['timestamp'])
        time2 = parse_timestamp(c2['timestamp'])
        time_diff_days = abs((time1 - time2).days)
        newer_idx = 0 if time1 > time2 else 1
        
        return {
            'weight_diff': weight_diff,
            'time_diff_days': time_diff_days,
            'higher_weight_idx': higher_weight_idx,
            'newer_idx': newer_idx
        }
    
    def identify_scenario(self, conflicts: List[Dict]) -> str:
        """场景识别"""
        if len(conflicts) != 2:
            return 'balanced'
        
        chars = self.analyze_conflict_characteristics(conflicts)
        weight_diff = chars['weight_diff']
        time_diff_days = chars['time_diff_days']
        
        # 使用相同的场景识别逻辑
        if weight_diff >= 0.25:
            return 'weight_dominant'
        elif time_diff_days >= 250:
            return 'time_dominant'
        elif weight_diff >= self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            return 'weight_dominant'
        elif time_diff_days >= self.time_critical_threshold and weight_diff < 0.20:
            return 'time_dominant'
        elif weight_diff >= 0.10 and time_diff_days >= self.time_significant_threshold:
            if time_diff_days >= 300:
                return 'complex_tradeoff_favor_recency'
            else:
                return 'complex_tradeoff_favor_authority'
        elif weight_diff < 0.10 and time_diff_days < 100:
            return 'edge_case'
        else:
            return 'balanced'
    
    def resolve_conflict(self, conflicts: List[Dict]) -> Dict:
        """解决冲突"""
        if not conflicts:
            return {}
        if len(conflicts) == 1:
            return conflicts[0]
        
        # 场景识别
        scenario = self.identify_scenario(conflicts)
        weights_config = self.scenario_weights[scenario]
        
        # 计算评分
        best_conflict = None
        best_score = -1
        
        for conflict in conflicts:
            weight = conflict['source']['weight']
            timestamp = parse_timestamp(conflict['timestamp'])
            content_length = len(conflict.get('value', ''))
            
            # 时间评分
            now = datetime.now(timestamp.tzinfo)
            days_old = (now - timestamp).days
            time_score = max(0, 1 - days_old / 730)
            
            # 内容评分
            content_score = min(1.0, content_length / 100)
            
            # 综合评分
            final_score = (weight * weights_config['weight'] + 
                          time_score * weights_config['time'] + 
                          content_score * weights_config['content'])
            
            if final_score > best_score:
                best_score = final_score
                best_conflict = conflict
        
        return best_conflict

def main():
    """主函数"""
    optimizer = GridSearchOptimizer()
    
    # 执行完整网格搜索
    results = optimizer.full_grid_search()
    
    # 保存结果
    with open('grid_search_results.json', 'w', encoding='utf-8') as f:
        # 转换为可序列化的格式
        serializable_results = {
            'baseline_accuracy': results['baseline_accuracy'],
            'final_accuracy': results['final_accuracy'],
            'improvement': results['improvement'],
            'optimized_config': results['optimized_config']
        }
        json.dump(serializable_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到 grid_search_results.json")

if __name__ == "__main__":
    main()
