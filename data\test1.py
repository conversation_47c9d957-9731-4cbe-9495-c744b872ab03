import json
import os
from datetime import datetime
import logging
from neo4j import GraphDatabase
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import Dict, List, Optional

# 配置日志，指定到代码所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'app.log.txt')
logging.basicConfig(filename=log_file, level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Neo4j 连接（需替换为你的实际配置）
driver = GraphDatabase.driver("bolt://localhost:7688", auth=("neo4j", "hxdhxdhxd6615"))

# 初始化语义相似度模型
model = SentenceTransformer('all-MiniLM-L6-v2')

# 关键词列表
KEYWORDS = ["景点名", "文化", "美食", "体验"]

def get_file_timestamp(file_path: str) -> str:
    """获取文件修改时间作为爬取时间戳"""
    mod_time = os.path.getmtime(file_path)
    return datetime.fromtimestamp(mod_time).strftime('%Y-%m-%dT%H:%M:%S+08:00')

def calculate_keyword_density(text: str, keywords: List[str]) -> float:
    """计算关键词密度"""
    if not text or not isinstance(text, str):
        return 0.0
    word_count = len(text.split())
    if word_count == 0:
        return 0.0
    keyword_count = sum(text.count(keyword) for keyword in keywords if keyword in text)
    return (keyword_count / word_count) * 100

def extract_best_comment(description: str) -> str:
    """从描述中提取最佳评论，基于关键字密度和相关性"""
    if not description or not isinstance(description, str):
        return "该景点信息官方尚未提供"
    sentences = description.split('.')
    best_score = -1
    best_sentence = ""
    for sentence in sentences:
        if not sentence.strip():
            continue
        keyword_density = calculate_keyword_density(sentence, KEYWORDS)
        # 简化相关性评分，需替换为实际 LLM 输出
        relevance_score = 0.7
        total_score = (0.3 * keyword_density / 100) + (0.7 * relevance_score)
        if total_score > best_score:
            best_score = total_score
            best_sentence = sentence.strip() + "."
    return best_sentence if best_sentence else "该景点信息官方尚未提供"

def process_chunk(data: Dict) -> Dict:
    """处理单个数据块，更新节点属性"""
    node = {
        "name": data.get("name", ""),
        "location": data.get("location", ""),
        "address": data.get("address", ""),
        "description": data.get("description", ""),
        "ranking": data.get("ranking", ""),
        "visitor_percentage": data.get("visitor_percentage", ""),
        "pub_timestamp": data.get("pub_timestamp", ""),
        "official_description": "",
        "best_comment": ""
    }

    # 补全空字段
    if not node["description"]:
        node["description"] = "该景点信息官方尚未提供"
    if not node["ranking"]:
        node["ranking"] = "排名未提供"

    # 提取最佳评论
    node["best_comment"] = extract_best_comment(node["description"])

    return node

def update_neo4j(tx, node: Dict, crawl_timestamp: str):
    """更新 Neo4j 图谱，包括节点关系"""
    # 创建节点
    query_node = """
    MERGE (n:Place {name: $name})
    SET n.location = $location,
        n.address = $address,
        n.description = $description,
        n.ranking = $ranking,
        n.visitor_percentage = $visitor_percentage,
        n.pub_timestamp = $pub_timestamp,
        n.crawl_timestamp = $crawl_timestamp,
        n.official_description = $official_description,
        n.best_comment = $best_comment
    """
    tx.run(query_node, **node, crawl_timestamp=crawl_timestamp)

    # 创建基于 location 的默认关系
    query_rel = """
    MATCH (a:Place {name: $name}), (b:Place)
    WHERE b.location = $location AND b.name <> $name
    MERGE (a)-[:LOCATED_IN_SAME_REGION]->(b)
    """
    tx.run(query_rel, name=node["name"], location=node["location"])

def detect_conflict(existing_node: Dict, new_node: Dict) -> bool:
    """检测节点属性冲突"""
    if not existing_node or not isinstance(existing_node, dict):
        return False
    fields = ["location", "description", "ranking"]
    for field in fields:
        if existing_node.get(field) and new_node.get(field):
            embeddings1 = model.encode(existing_node[field], convert_to_tensor=True)
            embeddings2 = model.encode(new_node[field], convert_to_tensor=True)
            similarity = np.dot(embeddings1, embeddings2) / (np.linalg.norm(embeddings1) * np.linalg.norm(embeddings2))
            if similarity < 0.7:
                return True
    return False

def resolve_conflict(tx, existing_node: Dict, new_node: Dict, crawl_timestamp: str) -> bool:
    """解决冲突，基于混合验证"""
    if not existing_node or not isinstance(existing_node, dict):
        return False
    votes = {
        "timestamp": 1 if new_node["pub_timestamp"] > existing_node.get("pub_timestamp", "1970-01-01") else 0,
        "rules": 1 if new_node["location"] in ["拉萨市", "当雄县", "墨竹工卡县"] else 0,
        "weight": 1 if calculate_weight(new_node) > calculate_weight(existing_node) else 0
    }
    if sum(votes.values()) >= 2:
        update_neo4j(tx, new_node, crawl_timestamp)
        return True
    else:
        conflict_file = os.path.join(script_dir, "conflict_queue.json")
        with open(conflict_file, "a", encoding='utf-8') as f:
            json.dump({"existing": existing_node, "new": new_node}, f)
            f.write("\n")
        logging.info(f"Conflict detected and queued for {new_node['name']}")
        return False

def calculate_weight(node: Dict) -> float:
    """计算数据权重"""
    source_rating = 0.8  # 当前文件权重
    timeliness = 0.1 if (datetime.now() - datetime.fromisoformat(node["pub_timestamp"].replace("+08:00", "+00:00"))).days <= 180 else -0.1
    completeness = len([v for v in node.values() if v]) / 7  # 7 个关键字段
    return 0.6 * source_rating + 0.2 * timeliness + 0.2 * completeness

def main():
    """主函数，处理 JSON 文件"""
    data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "D:\PythonCode\机器学习\动态知识图谱的自适应演化\data")
    for filename in os.listdir(data_dir):
        if filename.endswith(".json"):
            file_path = os.path.join(data_dir, filename)
            crawl_timestamp = get_file_timestamp(file_path)
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if "nodes" in data:
                    for node_data in data["nodes"]:
                        # 设置 pub_timestamp，若为空用 crawl_timestamp
                        if not node_data.get("pub_timestamp"):
                            node_data["pub_timestamp"] = crawl_timestamp
                        processed_node = process_chunk(node_data)
                        with driver.session() as session:
                            # 检查现有节点
                            result = session.run("MATCH (n:Place {name: $name}) RETURN n", name=processed_node["name"])
                            existing_node = result.single()  # 直接获取节点，若无结果为 None
                            if existing_node:
                                existing_props = dict(existing_node)
                                if detect_conflict(existing_props, processed_node):
                                    if not resolve_conflict(session, existing_props, processed_node, crawl_timestamp):
                                        continue
                                else:
                                    update_neo4j(session, processed_node, crawl_timestamp)
                                    logging.info(f"Updated node {processed_node['name']}")
                            else:
                                update_neo4j(session, processed_node, crawl_timestamp)
                                logging.info(f"Created new node {processed_node['name']}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error(f"Error occurred: {str(e)}")
        raise
    finally:
        driver.close()