# 核心依赖
neo4j>=5.0.0
langchain>=0.1.0
langchain-openai>=0.1.0
sentence-transformers>=2.2.0

# HTTP 客户端
httpx>=0.24.0
aiohttp>=3.8.0
requests>=2.28.0

# 重试和错误处理
tenacity>=8.2.0

# 数据处理
pandas>=1.5.0
numpy>=1.24.0

# 可视化
matplotlib>=3.6.0

# API 客户端
openai>=1.0.0
anthropic>=0.7.0
google-generativeai>=0.3.0

# 环境变量和配置
python-dotenv>=1.0.0

# JSON 处理
json5>=0.9.0

# 注意：以下是Python内置模块，不需要安装
# logging, asyncio, pathlib, typing, datetime, collections, dataclasses, os, sys, time, json