#!/usr/bin/env python3
"""
清洁版策略实验 - 只保留优化的自适应策略

对比Weight-First、Latest-First和优化自适应Hybrid三种策略
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict

def load_test_dataset():
    """加载测试数据集"""
    try:
        with open('lhasa_conflict_test_dataset.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载测试数据失败: {e}")
        return []

def parse_timestamp(timestamp_str: str) -> datetime:
    """解析时间戳"""
    try:
        return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    except:
        return datetime.now()

class OptimizedAdaptiveResolver:
    """优化的自适应混合冲突解决器"""
    
    def __init__(self):
        # 优化的场景识别阈值
        self.weight_significant_threshold = 0.15
        self.time_significant_threshold = 120
        self.time_critical_threshold = 200
        
        # 优化的权重配置
        self.scenario_weights = {
            'weight_dominant': {'weight': 0.80, 'time': 0.15, 'content': 0.05},
            'time_dominant': {'weight': 0.15, 'time': 0.80, 'content': 0.05},
            'complex_tradeoff_favor_authority': {'weight': 0.65, 'time': 0.30, 'content': 0.05},
            'complex_tradeoff_favor_recency': {'weight': 0.25, 'time': 0.70, 'content': 0.05},
            'edge_case': {'weight': 0.40, 'time': 0.35, 'content': 0.25},
            'balanced': {'weight': 0.45, 'time': 0.45, 'content': 0.10}
        }
        
        # 权威性层级
        self.authority_levels = {
            'government': 5, 'tourism_bureau': 5,
            'official_site': 4, 'academic': 4,
            'travel_guide': 3, 'news_media': 3,
            'travel_site': 2, 'forum': 1,
            'social_media': 1, 'personal_blog': 1
        }
    
    def analyze_conflict_characteristics(self, conflicts: List[Dict]) -> Dict:
        """深度分析冲突特征"""
        if len(conflicts) != 2:
            return {}
        
        c1, c2 = conflicts
        
        # 权重分析
        weight1, weight2 = c1['source']['weight'], c2['source']['weight']
        weight_diff = abs(weight1 - weight2)
        higher_weight_idx = 0 if weight1 > weight2 else 1
        
        # 时间分析
        time1 = parse_timestamp(c1['timestamp'])
        time2 = parse_timestamp(c2['timestamp'])
        time_diff_days = abs((time1 - time2).days)
        newer_idx = 0 if time1 > time2 else 1
        
        # 内容分析
        content1, content2 = c1.get('value', ''), c2.get('value', '')
        content1_len, content2_len = len(content1), len(content2)
        content_diff = abs(content1_len - content2_len)
        
        # 权威性层级分析
        auth1 = self.authority_levels.get(c1['source']['type'], 2)
        auth2 = self.authority_levels.get(c2['source']['type'], 2)
        authority_gap = abs(auth1 - auth2)
        
        return {
            'weight_diff': weight_diff,
            'time_diff_days': time_diff_days,
            'content_diff': content_diff,
            'authority_gap': authority_gap,
            'higher_weight_idx': higher_weight_idx,
            'newer_idx': newer_idx,
            'weights': [weight1, weight2],
            'times': [time1, time2],
            'authority_levels': [auth1, auth2]
        }
    
    def identify_scenario(self, conflicts: List[Dict]) -> str:
        """优化的场景识别算法"""
        if len(conflicts) != 2:
            return 'balanced'
        
        chars = self.analyze_conflict_characteristics(conflicts)
        weight_diff = chars['weight_diff']
        time_diff_days = chars['time_diff_days']
        authority_gap = chars['authority_gap']
        
        # 精确的场景识别逻辑
        if weight_diff >= 0.25 or authority_gap >= 3:
            return 'weight_dominant'
        elif time_diff_days >= 250:
            return 'time_dominant'
        elif weight_diff >= self.weight_significant_threshold and time_diff_days < self.time_significant_threshold:
            return 'weight_dominant'
        elif time_diff_days >= self.time_critical_threshold and weight_diff < 0.20:
            return 'time_dominant'
        elif weight_diff >= 0.10 and time_diff_days >= self.time_significant_threshold:
            if time_diff_days >= 300:
                return 'complex_tradeoff_favor_recency'
            else:
                return 'complex_tradeoff_favor_authority'
        elif weight_diff < 0.10 and time_diff_days < 100:
            return 'edge_case'
        else:
            return 'balanced'
    
    def calculate_smart_time_score(self, timestamp: str, scenario: str, time_diff_days: int) -> float:
        """智能时间评分"""
        time_obj = parse_timestamp(timestamp)
        now = datetime.now(time_obj.tzinfo)
        days_old = (now - time_obj).days
        
        if scenario == 'time_dominant':
            if time_diff_days > 200:
                decay_factor = 300
            else:
                decay_factor = 500
            time_score = max(0, 1 - (days_old / decay_factor) ** 2)
        elif scenario == 'complex_tradeoff_favor_recency':
            decay_factor = 400
            time_score = max(0, 1 - (days_old / decay_factor) ** 1.5)
        elif scenario == 'weight_dominant':
            decay_factor = 1500
            time_score = max(0, 1 - days_old / decay_factor)
        else:
            decay_factor = 730
            time_score = max(0, 1 - days_old / decay_factor)
        
        return time_score
    
    def resolve_conflict(self, conflicts: List[Dict]) -> Dict:
        """优化的冲突解决主函数"""
        if not conflicts:
            return {}
        
        if len(conflicts) == 1:
            return conflicts[0]
        
        # 深度特征分析
        chars = self.analyze_conflict_characteristics(conflicts)
        
        # 优化的场景识别
        scenario = self.identify_scenario(conflicts)
        weights_config = self.scenario_weights[scenario]
        
        # 特殊规则优先处理
        if chars['weight_diff'] >= 0.35:
            return conflicts[chars['higher_weight_idx']]
        
        if chars['time_diff_days'] >= 400:
            return conflicts[chars['newer_idx']]
        
        if chars['authority_gap'] >= 4:
            higher_auth_idx = 0 if chars['authority_levels'][0] > chars['authority_levels'][1] else 1
            return conflicts[higher_auth_idx]
        
        # 计算综合评分
        best_conflict = None
        best_score = -1
        
        for i, conflict in enumerate(conflicts):
            weight = conflict['source']['weight']
            timestamp = conflict['timestamp']
            content = conflict.get('value', '')
            
            # 智能时间评分
            time_score = self.calculate_smart_time_score(timestamp, scenario, chars['time_diff_days'])
            
            # 内容质量评分
            content_length = len(content)
            if scenario == 'edge_case':
                content_score = min(1.0, content_length / 60)
                quality_keywords = ['详细', '完整', '权威', '官方', '认证', '专业', '全面']
                for keyword in quality_keywords:
                    if keyword in content:
                        content_score += 0.05
                content_score = min(1.0, content_score)
            else:
                content_score = min(1.0, content_length / 100)
            
            # 综合评分
            final_score = (weight * weights_config['weight'] + 
                          time_score * weights_config['time'] + 
                          content_score * weights_config['content'])
            
            if final_score > best_score:
                best_score = final_score
                best_conflict = conflict
        
        return best_conflict

def resolve_conflict_weight_first(conflicts: List[Dict]) -> Dict:
    """权重优先策略"""
    if not conflicts:
        return {}
    return max(conflicts, key=lambda x: x.get('source', {}).get('weight', 0))

def resolve_conflict_latest_first(conflicts: List[Dict]) -> Dict:
    """时间优先策略"""
    if not conflicts:
        return {}
    return max(conflicts, key=lambda x: parse_timestamp(x.get('timestamp', '')))

def run_strategy_test(test_cases: List[Dict], strategy_func, strategy_name: str):
    """运行单个策略的测试"""
    print(f"\n🔄 测试策略: {strategy_name}")
    
    correct_count = 0
    results = []
    start_time = time.time()
    
    # 统计数据
    results_by_type = defaultdict(lambda: {'correct': 0, 'total': 0})
    results_by_difficulty = defaultdict(lambda: {'correct': 0, 'total': 0})
    scenario_distribution = defaultdict(int)
    
    for case in test_cases:
        conflicts = case['conflicts']
        expected_id = case['expected_winner_id']
        conflict_type = case['conflict_type']
        difficulty = case['difficulty']
        
        # 应用策略
        if hasattr(strategy_func, 'resolve_conflict'):
            winner = strategy_func.resolve_conflict(conflicts)
            # 记录场景识别
            scenario = strategy_func.identify_scenario(conflicts)
            scenario_distribution[scenario] += 1
        else:
            winner = strategy_func(conflicts)
        
        winner_id = winner.get('source', {}).get('url', '') if winner else ''
        
        is_correct = winner_id == expected_id
        if is_correct:
            correct_count += 1
        
        results.append({
            'case_id': case['id'],
            'case_name': case['name'],
            'conflict_type': conflict_type,
            'difficulty': difficulty,
            'expected': expected_id,
            'chosen': winner_id,
            'correct': is_correct
        })
        
        # 按类型统计
        results_by_type[conflict_type]['total'] += 1
        if is_correct:
            results_by_type[conflict_type]['correct'] += 1
        
        # 按难度统计
        results_by_difficulty[difficulty]['total'] += 1
        if is_correct:
            results_by_difficulty[difficulty]['correct'] += 1
    
    end_time = time.time()
    execution_time = end_time - start_time
    accuracy = correct_count / len(test_cases) if test_cases else 0
    
    print(f"   ✅ 准确率: {accuracy:.3f} ({correct_count}/{len(test_cases)})")
    print(f"   ⏱️  执行时间: {execution_time:.4f}秒")
    
    return {
        'strategy': strategy_name,
        'accuracy': accuracy,
        'correct_count': correct_count,
        'total_count': len(test_cases),
        'execution_time': execution_time,
        'results': results,
        'by_type': dict(results_by_type),
        'by_difficulty': dict(results_by_difficulty),
        'scenario_distribution': dict(scenario_distribution) if scenario_distribution else {}
    }

def analyze_detailed_results(all_results: List[Dict]):
    """详细结果分析"""
    print(f"\n📊 详细结果分析")
    print("=" * 70)
    
    # 整体性能对比
    print(f"\n📈 整体性能对比:")
    print(f"{'策略':<20} {'准确率':<10} {'正确数':<10} {'执行时间':<12} {'改进幅度':<10}")
    print("-" * 70)
    
    baseline_acc = all_results[0]['accuracy']  # Weight-First作为基准
    
    for result in all_results:
        improvement = result['accuracy'] - baseline_acc
        improvement_str = f"+{improvement:.3f}" if improvement > 0 else f"{improvement:.3f}"
        print(f"{result['strategy']:<20} {result['accuracy']:.3f}     {result['correct_count']:<10} {result['execution_time']:.4f}s     {improvement_str:<10}")
    
    # 按冲突类型对比
    print(f"\n📊 按冲突类型对比:")
    print(f"{'冲突类型':<20} {'Weight-First':<15} {'Latest-First':<15} {'自适应Hybrid':<15}")
    print("-" * 70)
    
    conflict_types = ['weight_dominant', 'time_dominant', 'complex_tradeoff', 'edge_case']
    
    for conflict_type in conflict_types:
        row = f"{conflict_type:<20}"
        for result in all_results:
            if conflict_type in result['by_type']:
                stats = result['by_type'][conflict_type]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                count_str = f"{acc:.3f}({stats['correct']}/{stats['total']})"
                row += f" {count_str:<15}"
            else:
                row += f"{'N/A':<15}"
        print(row)
    
    # 按难度对比
    print(f"\n📊 按难度等级对比:")
    print(f"{'难度等级':<15} {'Weight-First':<15} {'Latest-First':<15} {'自适应Hybrid':<15}")
    print("-" * 65)
    
    difficulties = ['easy', 'medium', 'hard']
    
    for difficulty in difficulties:
        row = f"{difficulty:<15}"
        for result in all_results:
            if difficulty in result['by_difficulty']:
                stats = result['by_difficulty'][difficulty]
                acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                count_str = f"{acc:.3f}({stats['correct']}/{stats['total']})"
                row += f" {count_str:<15}"
            else:
                row += f"{'N/A':<15}"
        print(row)
    
    # 自适应策略的场景识别分析
    adaptive_result = all_results[2]  # 自适应策略结果
    if 'scenario_distribution' in adaptive_result and adaptive_result['scenario_distribution']:
        print(f"\n🎯 自适应策略场景识别分布:")
        total_cases = sum(adaptive_result['scenario_distribution'].values())
        for scenario, count in adaptive_result['scenario_distribution'].items():
            percentage = count / total_cases * 100
            print(f"   {scenario}: {count}个 ({percentage:.1f}%)")

def create_final_summary(all_results: List[Dict], test_cases: List[Dict]):
    """创建最终总结"""
    print(f"\n🏆 实验总结报告")
    print("=" * 70)
    
    print(f"🎯 基于拉萨知识图谱的冲突解决策略对比实验")
    print(f"📊 测试数据: {len(test_cases)} 个基于真实景点数据的冲突案例")
    print(f"🕒 实验时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 找出最佳策略
    best_overall = max(all_results, key=lambda x: x['accuracy'])
    print(f"\n🥇 最佳整体策略: {best_overall['strategy']} (准确率: {best_overall['accuracy']:.3f})")
    
    # 困难场景最佳
    best_hard = max(all_results, 
                   key=lambda x: x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / 
                                x['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total'])
    hard_acc = best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['correct'] / \
               best_hard['by_difficulty'].get('hard', {'correct': 0, 'total': 1})['total']
    print(f"🎯 困难场景最佳: {best_hard['strategy']} (准确率: {hard_acc:.3f})")
    
    # 改进分析
    weight_first_acc = all_results[0]['accuracy']
    latest_first_acc = all_results[1]['accuracy']
    adaptive_acc = all_results[2]['accuracy']
    
    print(f"\n📊 关键发现:")
    print(f"   • Weight-First准确率: {weight_first_acc:.3f}")
    print(f"   • Latest-First准确率: {latest_first_acc:.3f}")
    print(f"   • 自适应Hybrid准确率: {adaptive_acc:.3f}")
    
    improvement_over_weight = adaptive_acc - weight_first_acc
    improvement_over_latest = adaptive_acc - latest_first_acc
    print(f"   ✅ 自适应相比Weight-First提升: {improvement_over_weight:+.3f} ({improvement_over_weight*100:+.1f}%)")
    print(f"   ✅ 自适应相比Latest-First提升: {improvement_over_latest:+.3f} ({improvement_over_latest*100:+.1f}%)")
    
    print(f"\n💡 自适应策略的核心优势:")
    print(f"   • 🎯 智能场景识别: 根据冲突特征自动识别场景类型")
    print(f"   • 🔧 动态权重调整: 不同场景使用不同的权重配置")
    print(f"   • 🛡️ 特殊规则保护: 极端情况下直接应用规则决策")
    print(f"   • 📈 增强评分机制: 智能的时间和内容质量评分")

def main():
    """主函数"""
    print("🚀 基于优化自适应策略的冲突解决实验")
    print("=" * 70)
    
    # 加载测试数据
    test_cases = load_test_dataset()
    if not test_cases:
        print("❌ 无法加载测试数据，程序退出")
        return
    
    print(f"✅ 成功加载 {len(test_cases)} 个测试案例")
    
    # 统计测试集信息
    type_counts = defaultdict(int)
    difficulty_counts = defaultdict(int)
    
    for case in test_cases:
        type_counts[case['conflict_type']] += 1
        difficulty_counts[case['difficulty']] += 1
    
    print(f"📊 测试集分布:")
    print(f"   冲突类型: {dict(type_counts)}")
    print(f"   难度分布: {dict(difficulty_counts)}")
    
    # 初始化策略
    adaptive_resolver = OptimizedAdaptiveResolver()
    
    # 定义策略 - 只保留三种策略
    strategies = [
        (resolve_conflict_weight_first, "Weight-First"),
        (resolve_conflict_latest_first, "Latest-First"),
        (adaptive_resolver, "自适应Hybrid")
    ]
    
    # 运行所有策略测试
    all_results = []
    for strategy_func, strategy_name in strategies:
        result = run_strategy_test(test_cases, strategy_func, strategy_name)
        all_results.append(result)
    
    # 详细分析
    analyze_detailed_results(all_results)
    
    # 创建最终总结
    create_final_summary(all_results, test_cases)
    
    print(f"\n🎉 实验完成！")
    print(f"📁 基于您的拉萨知识图谱数据生成的测试集: lhasa_conflict_test_dataset.json")

if __name__ == "__main__":
    main()
