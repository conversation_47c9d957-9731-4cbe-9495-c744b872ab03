#!/usr/bin/env python3
"""
增强的冲突解决模块

为实验提供详细的冲突检测、解决和统计功能，
支持多策略代理的实体消歧、关系推断和版本控制。
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import asyncio

from neo4j_crud import Neo4jCRUD
from utils import compute_semantic_similarity, compute_dynamic_weight

logger = logging.getLogger(__name__)

@dataclass
class ConflictStatistics:
    """冲突统计信息"""
    total_conflicts: int = 0
    resolved_conflicts: int = 0
    unresolved_conflicts: int = 0
    resolution_methods: Dict[str, int] = None
    
    def __post_init__(self):
        if self.resolution_methods is None:
            self.resolution_methods = {
                "timestamp_priority": 0,
                "source_weight": 0,
                "llm_reasoning": 0,
                "manual_intervention": 0,
                "semantic_similarity": 0
            }
    
    @property
    def resolution_rate(self) -> float:
        """冲突解决率"""
        return self.resolved_conflicts / self.total_conflicts if self.total_conflicts > 0 else 1.0

class EnhancedConflictResolver:
    """增强的冲突解决器"""
    
    def __init__(self, crud: Neo4jCRUD, enable_llm_reasoning: bool = True):
        self.crud = crud
        self.enable_llm_reasoning = enable_llm_reasoning
        self.statistics = ConflictStatistics()
        self.conflict_log = []
        
        # 冲突解决规则权重
        self.source_weights = {
            "government": 1.0,
            "academic": 0.9,
            "professional_guide": 0.8,
            "news": 0.6,
            "social_media": 0.4,
            "user_generated": 0.3
        }
        
        logger.info("增强冲突解决器初始化完成")
    
    def detect_entity_conflict(self, new_entity: Dict, existing_entity: Dict) -> Tuple[bool, List[str]]:
        """检测实体冲突"""
        conflicts = []
        
        # 检查关键字段冲突
        key_fields = ["category", "location", "description", "visitor_percentage", "ranking"]
        
        for field in key_fields:
            new_value = new_entity.get(field)
            existing_value = existing_entity.get(field)
            
            if new_value and existing_value and new_value != existing_value:
                # 对于数值字段，检查差异是否显著
                if field == "visitor_percentage":
                    if self._is_significant_percentage_difference(new_value, existing_value):
                        conflicts.append(f"{field}: {existing_value} vs {new_value}")
                elif field == "ranking":
                    if abs(int(new_value) - int(existing_value)) > 1:
                        conflicts.append(f"{field}: {existing_value} vs {new_value}")
                else:
                    # 对于文本字段，使用语义相似度
                    similarity = compute_semantic_similarity(str(existing_value), str(new_value))
                    if similarity < 0.8:  # 相似度阈值
                        conflicts.append(f"{field}: {existing_value} vs {new_value}")
        
        has_conflict = len(conflicts) > 0
        if has_conflict:
            self.statistics.total_conflicts += 1
            logger.debug(f"检测到实体冲突: {new_entity.get('name')}, 冲突字段: {conflicts}")
        
        return has_conflict, conflicts
    
    def _is_significant_percentage_difference(self, value1: str, value2: str, threshold: float = 10.0) -> bool:
        """检查百分比差异是否显著"""
        try:
            pct1 = float(str(value1).strip('%'))
            pct2 = float(str(value2).strip('%'))
            return abs(pct1 - pct2) > threshold
        except (ValueError, TypeError):
            return True  # 如果无法解析，认为有冲突
    
    async def resolve_entity_conflict(self, new_entity: Dict, existing_entity: Dict, 
                                    conflict_fields: List[str]) -> Tuple[Dict, str]:
        """解决实体冲突"""
        resolution_method = "unknown"
        resolved_entity = existing_entity.copy()
        
        try:
            # 1. 基于时间戳优先级
            new_timestamp = self._parse_timestamp(new_entity.get("pub_timestamp"))
            existing_timestamp = self._parse_timestamp(existing_entity.get("pub_timestamp"))
            
            # 2. 基于来源权重
            new_weight = self._get_source_weight(new_entity.get("source_type", "unknown"))
            existing_weight = self._get_source_weight(existing_entity.get("source_type", "unknown"))
            
            # 3. 综合决策
            if new_weight > existing_weight + 0.2:  # 来源权重显著更高
                resolved_entity = new_entity.copy()
                resolution_method = "source_weight"
                self.statistics.resolution_methods["source_weight"] += 1
            elif new_timestamp and existing_timestamp and new_timestamp > existing_timestamp:
                # 时间更新且来源权重相近
                if abs(new_weight - existing_weight) <= 0.2:
                    resolved_entity = new_entity.copy()
                    resolution_method = "timestamp_priority"
                    self.statistics.resolution_methods["timestamp_priority"] += 1
            elif self.enable_llm_reasoning:
                # 使用LLM推理解决复杂冲突
                resolved_entity = await self._llm_conflict_resolution(new_entity, existing_entity, conflict_fields)
                resolution_method = "llm_reasoning"
                self.statistics.resolution_methods["llm_reasoning"] += 1
            else:
                # 保持现有实体，记录冲突
                resolution_method = "manual_intervention"
                self.statistics.resolution_methods["manual_intervention"] += 1
                self.statistics.unresolved_conflicts += 1
                
                # 记录未解决的冲突
                self._log_unresolved_conflict(new_entity, existing_entity, conflict_fields)
                return resolved_entity, resolution_method
            
            self.statistics.resolved_conflicts += 1
            logger.info(f"冲突已解决: {resolved_entity.get('name')}, 方法: {resolution_method}")
            
        except Exception as e:
            logger.error(f"解决冲突失败: {e}")
            resolution_method = "error"
            self.statistics.unresolved_conflicts += 1
        
        return resolved_entity, resolution_method
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳"""
        if not timestamp_str:
            return None
        
        try:
            # 尝试多种时间格式
            formats = [
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str.split('+')[0], fmt)
                except ValueError:
                    continue
            
            return None
        except Exception:
            return None
    
    def _get_source_weight(self, source_type: str) -> float:
        """获取来源权重"""
        return self.source_weights.get(source_type.lower(), 0.5)
    
    async def _llm_conflict_resolution(self, new_entity: Dict, existing_entity: Dict, 
                                     conflict_fields: List[str]) -> Dict:
        """使用LLM推理解决冲突"""
        # 这里可以集成实际的LLM调用
        # 目前使用简化的逻辑
        
        # 基于内容质量选择
        new_content_score = self._calculate_content_quality(new_entity)
        existing_content_score = self._calculate_content_quality(existing_entity)
        
        if new_content_score > existing_content_score:
            return new_entity.copy()
        else:
            return existing_entity.copy()
    
    def _calculate_content_quality(self, entity: Dict) -> float:
        """计算内容质量分数"""
        score = 0.0
        
        # 描述长度和质量
        description = entity.get("description", "")
        if description:
            score += min(len(description) / 100, 1.0) * 0.3
        
        # 字段完整性
        required_fields = ["name", "category", "location", "description", "visitor_percentage"]
        completeness = sum(1 for field in required_fields if entity.get(field)) / len(required_fields)
        score += completeness * 0.4
        
        # 数据新鲜度
        timestamp = self._parse_timestamp(entity.get("pub_timestamp"))
        if timestamp:
            days_old = (datetime.now() - timestamp).days
            freshness = max(0, 1 - days_old / 365)  # 一年内的数据认为是新鲜的
            score += freshness * 0.3
        
        return score
    
    def _log_unresolved_conflict(self, new_entity: Dict, existing_entity: Dict, conflict_fields: List[str]):
        """记录未解决的冲突"""
        conflict_record = {
            "timestamp": datetime.now().isoformat(),
            "entity_name": new_entity.get("name"),
            "conflict_fields": conflict_fields,
            "new_entity": new_entity,
            "existing_entity": existing_entity,
            "status": "unresolved"
        }
        
        self.conflict_log.append(conflict_record)
        
        # 保存到文件
        try:
            with open("unresolved_conflicts.json", "a", encoding="utf-8") as f:
                json.dump(conflict_record, f, ensure_ascii=False)
                f.write("\n")
        except Exception as e:
            logger.error(f"保存未解决冲突失败: {e}")
    
    async def detect_relationship_conflicts(self, new_relationships: List[Dict], 
                                          existing_relationships: List[Dict]) -> List[Tuple[Dict, Dict, List[str]]]:
        """检测关系冲突"""
        conflicts = []
        
        for new_rel in new_relationships:
            for existing_rel in existing_relationships:
                if (new_rel.get("source") == existing_rel.get("source") and
                    new_rel.get("target") == existing_rel.get("target") and
                    new_rel.get("type") == existing_rel.get("type")):
                    
                    # 检查属性冲突
                    new_props = new_rel.get("properties", {})
                    existing_props = existing_rel.get("properties", {})
                    
                    conflict_props = []
                    for key in set(new_props.keys()) | set(existing_props.keys()):
                        if new_props.get(key) != existing_props.get(key):
                            conflict_props.append(key)
                    
                    if conflict_props:
                        conflicts.append((new_rel, existing_rel, conflict_props))
                        self.statistics.total_conflicts += 1
        
        return conflicts
    
    def get_statistics(self) -> ConflictStatistics:
        """获取冲突统计信息"""
        return self.statistics
    
    def reset_statistics(self):
        """重置统计信息"""
        self.statistics = ConflictStatistics()
        self.conflict_log = []
    
    def export_conflict_report(self, filepath: str = "conflict_report.json"):
        """导出冲突报告"""
        report = {
            "statistics": {
                "total_conflicts": self.statistics.total_conflicts,
                "resolved_conflicts": self.statistics.resolved_conflicts,
                "unresolved_conflicts": self.statistics.unresolved_conflicts,
                "resolution_rate": self.statistics.resolution_rate,
                "resolution_methods": self.statistics.resolution_methods
            },
            "conflict_log": self.conflict_log,
            "generated_at": datetime.now().isoformat()
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"冲突报告已导出到 {filepath}")
        except Exception as e:
            logger.error(f"导出冲突报告失败: {e}")

# 实体消歧功能
class EntityDisambiguation:
    """实体消歧模块"""
    
    def __init__(self, crud: Neo4jCRUD):
        self.crud = crud
    
    async def disambiguate_entity(self, entity: Dict, candidates: List[Dict]) -> Optional[Dict]:
        """实体消歧"""
        if not candidates:
            return None
        
        best_match = None
        best_score = 0.0
        
        for candidate in candidates:
            score = await self._calculate_similarity_score(entity, candidate)
            if score > best_score:
                best_score = score
                best_match = candidate
        
        # 只有相似度超过阈值才认为是同一实体
        if best_score > 0.8:
            return best_match
        
        return None
    
    async def _calculate_similarity_score(self, entity1: Dict, entity2: Dict) -> float:
        """计算实体相似度分数"""
        score = 0.0
        
        # 名称相似度 (权重: 0.4)
        name1 = entity1.get("name", "")
        name2 = entity2.get("name", "")
        if name1 and name2:
            name_similarity = compute_semantic_similarity(name1, name2)
            score += name_similarity * 0.4
        
        # 位置相似度 (权重: 0.3)
        loc1 = entity1.get("location", "")
        loc2 = entity2.get("location", "")
        if loc1 and loc2:
            if loc1 == loc2:
                score += 0.3
            elif loc1 in loc2 or loc2 in loc1:
                score += 0.15
        
        # 类别相似度 (权重: 0.2)
        cat1 = entity1.get("category", "")
        cat2 = entity2.get("category", "")
        if cat1 and cat2:
            if cat1 == cat2:
                score += 0.2
            else:
                cat_similarity = compute_semantic_similarity(cat1, cat2)
                score += cat_similarity * 0.2
        
        # 描述相似度 (权重: 0.1)
        desc1 = entity1.get("description", "")
        desc2 = entity2.get("description", "")
        if desc1 and desc2:
            desc_similarity = compute_semantic_similarity(desc1, desc2)
            score += desc_similarity * 0.1
        
        return score
