# 🤖 AI多模型交叉验证系统

## 📋 概述

这个系统使用GPT-4、<PERSON>和Gemini三个AI模型对冲突案例进行独立判断，通过一致性分析提供更客观的"正确答案"，解决了原始预设答案的主观性问题。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements_validation.txt

# 配置API密钥
cp config.env.example config.env
# 编辑config.env，填入您的API密钥
```

### 2. 配置API密钥

在 `config.env` 文件中填入您的API密钥：

```env
OPENAI_API_KEY=sk-your-openai-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
GOOGLE_API_KEY=your-google-api-key-here
```

### 3. 单个案例测试

```bash
# 测试单个案例
python multi_model_validator.py
```

### 4. 批量验证

```bash
# 批量验证所有案例
python batch_validation.py
```

### 5. 结果对比分析

```bash
# 对比AI验证结果与原始答案
python compare_validation_results.py
```

## 📊 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     GPT-4       │    │     Claude      │    │     Gemini      │
│   独立判断      │    │   独立判断      │    │   独立判断      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      共识分析器           │
                    │   - 一致性计算           │
                    │   - 置信度评估           │
                    │   - 推理摘要生成         │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      最终判断             │
                    │   - 多数选择             │
                    │   - 加权置信度           │
                    │   - 详细解释             │
                    └───────────────────────────┘
```

## 🔧 核心功能

### 1. 多模型独立判断

每个AI模型独立分析冲突案例，从以下角度评估：

- **权威性** (40%权重): 信息源的官方程度和专业性
- **时效性** (35%权重): 信息的新旧程度和时间敏感度  
- **内容质量** (25%权重): 信息的详细程度和完整性

### 2. 共识分析

- **一致性计算**: 计算模型间的选择一致率
- **置信度评估**: 综合各模型的置信度
- **冲突解决**: 处理模型间的分歧

### 3. 结果验证

- **与原始答案对比**: 分析AI验证与预设答案的差异
- **置信度过滤**: 只采用高置信度的AI判断
- **数据集更新**: 基于AI验证结果更新测试数据集

## 📈 验证流程

### 阶段1: 单模型查询

```python
# 每个模型独立判断
gpt4_result = validator.query_gpt4(conflict_description)
claude_result = validator.query_claude(conflict_description)
gemini_result = validator.query_gemini(conflict_description)
```

### 阶段2: 共识分析

```python
# 分析一致性
consensus = validator.analyze_consensus([gpt4_result, claude_result, gemini_result])

# 结果包含：
# - final_choice: 'A' 或 'B'
# - agreement_rate: 0.67 (2/3一致)
# - confidence: 0.85 (加权置信度)
```

### 阶段3: 质量控制

```python
# 只采用高质量的验证结果
if (consensus.confidence >= 0.6 and 
    consensus.agreement_rate >= 0.6):
    # 更新期望结果
    case['expected_winner_id'] = new_winner_id
```

## 📊 输出结果

### 1. 详细验证结果

```json
{
  "case_id": 1,
  "case_name": "权重主导(easy) - 布达拉宫描述冲突",
  "ai_consensus_choice": "A",
  "agreement_rate": 1.0,
  "confidence": 0.92,
  "individual_judgments": [
    {
      "model": "GPT-4",
      "choice": "A", 
      "confidence": 0.9,
      "success": true
    }
  ],
  "reasoning_summary": "所有模型一致选择A..."
}
```

### 2. 分析报告

```json
{
  "summary": {
    "total_cases": 100,
    "agreement_with_original": 85,
    "overall_agreement_rate": 0.85
  },
  "by_conflict_type": {
    "weight_dominant": {
      "agreement_rate": 0.95,
      "avg_confidence": 0.88
    }
  }
}
```

### 3. 可视化图表

- 策略准确率对比图
- AI验证改进效果图
- 模型一致性分析图

## ⚙️ 配置选项

### API配置

```env
# 模型选择
OPENAI_MODEL=gpt-4
ANTHROPIC_MODEL=claude-3-sonnet-20240229
GOOGLE_MODEL=gemini-pro

# 质量控制
MIN_AGREEMENT_RATE=0.6      # 最小一致率
MIN_CONFIDENCE_THRESHOLD=0.7 # 最小置信度
MAX_RETRIES=3               # 最大重试次数
REQUEST_TIMEOUT=30          # 请求超时时间
```

### 批量处理配置

```python
# 批量验证参数
batch_size = 10        # 每批处理案例数
delay_seconds = 2      # 请求间隔时间
start_index = 0        # 开始索引
```

## 💰 成本估算

基于API调用费用估算（100个案例）：

- **GPT-4**: ~$5-10 (取决于输入长度)
- **Claude**: ~$3-6
- **Gemini**: ~$1-3
- **总计**: ~$9-19

## 🔍 质量保证

### 1. 错误处理

- API调用失败自动重试
- 响应解析错误处理
- 部分失败的优雅降级

### 2. 结果验证

- 响应格式验证
- 置信度范围检查
- 一致性阈值控制

### 3. 中间结果保存

- 每5个案例保存中间结果
- 支持断点续传
- 详细的日志记录

## 📝 使用示例

### 验证单个案例

```python
from multi_model_validator import MultiModelValidator

validator = MultiModelValidator()
conflicts = [conflict_a, conflict_b]
result = validator.get_consensus_judgment(conflicts)

print(f"AI选择: {result.final_choice}")
print(f"一致率: {result.agreement_rate}")
print(f"置信度: {result.confidence}")
```

### 批量验证

```python
from batch_validation import BatchValidator

batch_validator = BatchValidator()
test_cases = batch_validator.load_test_dataset()

results = batch_validator.validate_batch(
    test_cases, 
    start_index=0,
    batch_size=10,
    delay_seconds=2
)
```

## 🚨 注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制
2. **成本控制**: 大批量验证前先测试小样本
3. **速率限制**: 遵守各API的速率限制
4. **结果解释**: AI判断仍有主观性，需要人工审核关键案例

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   解决方案: 检查config.env中的API密钥是否正确
   ```

2. **网络连接问题**
   ```
   解决方案: 检查网络连接，考虑使用代理
   ```

3. **响应解析失败**
   ```
   解决方案: 检查模型响应格式，可能需要调整解析逻辑
   ```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 单个模型测试
validator = MultiModelValidator()
result = validator.query_gpt4(conflict_description)
print(result.reasoning)
```

## 📞 支持

如有问题或建议，请：

1. 检查日志文件中的错误信息
2. 确认API密钥和网络连接
3. 尝试单个案例测试
4. 查看示例代码和配置

---

*基于GPT-4、Claude和Gemini的多模型交叉验证系统*
