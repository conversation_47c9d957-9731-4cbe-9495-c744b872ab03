#!/usr/bin/env python3
"""
批量验证脚本

对所有测试案例进行多模型验证，生成新的"正确答案"
"""

import json
import time
import pandas as pd
from typing import List, Dict
from datetime import datetime
import logging
from pathlib import Path

from multi_model_validator import MultiModelValidator, ConsensusResult

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchValidator:
    """批量验证器"""
    
    def __init__(self):
        self.validator = MultiModelValidator()
        self.results = []
        
    def load_test_dataset(self, file_path: str = 'lhasa_conflict_test_dataset.json') -> List[Dict]:
        """加载测试数据集"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            logger.info(f"✅ 成功加载 {len(dataset)} 个测试案例")
            return dataset
        except Exception as e:
            logger.error(f"❌ 加载测试数据失败: {e}")
            return []
    
    def validate_single_case(self, case: Dict) -> Dict:
        """验证单个案例"""
        
        case_id = case.get('id', 'unknown')
        case_name = case.get('name', 'unknown')
        
        logger.info(f"🔄 验证案例 {case_id}: {case_name}")
        
        try:
            # 获取多模型共识
            consensus = self.validator.get_consensus_judgment(case['conflicts'])
            
            # 转换选择为URL格式
            if consensus.final_choice == 'A':
                ai_winner_id = case['conflicts'][0]['source']['url']
            elif consensus.final_choice == 'B':
                ai_winner_id = case['conflicts'][1]['source']['url']
            else:
                ai_winner_id = ""
            
            # 比较与原始预设答案
            original_winner = case.get('expected_winner_id', '')
            agreement_with_original = (ai_winner_id == original_winner)
            
            result = {
                'case_id': case_id,
                'case_name': case_name,
                'conflict_type': case.get('conflict_type', ''),
                'difficulty': case.get('difficulty', ''),
                'original_expected': original_winner,
                'ai_consensus_choice': consensus.final_choice,
                'ai_consensus_winner': ai_winner_id,
                'agreement_rate': consensus.agreement_rate,
                'confidence': consensus.confidence,
                'agrees_with_original': agreement_with_original,
                'individual_judgments': [
                    {
                        'model': j.model_name,
                        'choice': j.choice,
                        'confidence': j.confidence,
                        'success': j.success
                    } for j in consensus.individual_judgments
                ],
                'reasoning_summary': consensus.reasoning_summary,
                'validation_timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 案例 {case_id} 验证完成: {consensus.final_choice} (一致率: {consensus.agreement_rate:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 案例 {case_id} 验证失败: {e}")
            return {
                'case_id': case_id,
                'case_name': case_name,
                'error': str(e),
                'validation_timestamp': datetime.now().isoformat()
            }
    
    def validate_batch(self, test_cases: List[Dict], 
                      start_index: int = 0, 
                      batch_size: int = None,
                      delay_seconds: int = 2) -> List[Dict]:
        """批量验证"""
        
        if batch_size is None:
            batch_size = len(test_cases)
        
        end_index = min(start_index + batch_size, len(test_cases))
        batch_cases = test_cases[start_index:end_index]
        
        logger.info(f"🚀 开始批量验证: 案例 {start_index+1}-{end_index} (共 {len(batch_cases)} 个)")
        
        results = []
        
        for i, case in enumerate(batch_cases):
            try:
                # 验证单个案例
                result = self.validate_single_case(case)
                results.append(result)
                
                # 保存中间结果
                if (i + 1) % 5 == 0:
                    self.save_intermediate_results(results, start_index + i + 1)
                
                # 延迟以避免API限制
                if i < len(batch_cases) - 1:
                    time.sleep(delay_seconds)
                    
            except Exception as e:
                logger.error(f"❌ 批量验证中断: {e}")
                break
        
        logger.info(f"✅ 批量验证完成: {len(results)} 个案例")
        return results
    
    def save_intermediate_results(self, results: List[Dict], current_index: int):
        """保存中间结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"validation_results_intermediate_{current_index}_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 中间结果已保存: {filename}")
        except Exception as e:
            logger.error(f"❌ 保存中间结果失败: {e}")
    
    def analyze_validation_results(self, results: List[Dict]) -> Dict:
        """分析验证结果"""
        
        if not results:
            return {}
        
        # 过滤成功的验证结果
        successful_results = [r for r in results if 'error' not in r]
        
        if not successful_results:
            return {'error': '没有成功的验证结果'}
        
        # 基本统计
        total_cases = len(successful_results)
        agreement_with_original = sum(1 for r in successful_results if r.get('agrees_with_original', False))
        agreement_rate = agreement_with_original / total_cases
        
        # 按冲突类型分析
        by_conflict_type = {}
        for result in successful_results:
            conflict_type = result.get('conflict_type', 'unknown')
            if conflict_type not in by_conflict_type:
                by_conflict_type[conflict_type] = {
                    'total': 0,
                    'agreement_with_original': 0,
                    'avg_confidence': 0,
                    'avg_consensus_rate': 0
                }
            
            stats = by_conflict_type[conflict_type]
            stats['total'] += 1
            if result.get('agrees_with_original', False):
                stats['agreement_with_original'] += 1
            stats['avg_confidence'] += result.get('confidence', 0)
            stats['avg_consensus_rate'] += result.get('agreement_rate', 0)
        
        # 计算平均值
        for stats in by_conflict_type.values():
            if stats['total'] > 0:
                stats['agreement_rate'] = stats['agreement_with_original'] / stats['total']
                stats['avg_confidence'] /= stats['total']
                stats['avg_consensus_rate'] /= stats['total']
        
        # 按难度分析
        by_difficulty = {}
        for result in successful_results:
            difficulty = result.get('difficulty', 'unknown')
            if difficulty not in by_difficulty:
                by_difficulty[difficulty] = {
                    'total': 0,
                    'agreement_with_original': 0,
                    'avg_confidence': 0
                }
            
            stats = by_difficulty[difficulty]
            stats['total'] += 1
            if result.get('agrees_with_original', False):
                stats['agreement_with_original'] += 1
            stats['avg_confidence'] += result.get('confidence', 0)
        
        # 计算平均值
        for stats in by_difficulty.values():
            if stats['total'] > 0:
                stats['agreement_rate'] = stats['agreement_with_original'] / stats['total']
                stats['avg_confidence'] /= stats['total']
        
        # 模型一致性分析
        model_performance = {}
        for result in successful_results:
            for judgment in result.get('individual_judgments', []):
                model = judgment['model']
                if model not in model_performance:
                    model_performance[model] = {
                        'total_cases': 0,
                        'successful_cases': 0,
                        'avg_confidence': 0
                    }
                
                perf = model_performance[model]
                perf['total_cases'] += 1
                if judgment['success']:
                    perf['successful_cases'] += 1
                    perf['avg_confidence'] += judgment['confidence']
        
        # 计算模型成功率
        for perf in model_performance.values():
            if perf['successful_cases'] > 0:
                perf['success_rate'] = perf['successful_cases'] / perf['total_cases']
                perf['avg_confidence'] /= perf['successful_cases']
            else:
                perf['success_rate'] = 0
        
        return {
            'summary': {
                'total_cases': total_cases,
                'successful_validations': len(successful_results),
                'agreement_with_original': agreement_with_original,
                'overall_agreement_rate': agreement_rate
            },
            'by_conflict_type': by_conflict_type,
            'by_difficulty': by_difficulty,
            'model_performance': model_performance
        }
    
    def save_results(self, results: List[Dict], analysis: Dict):
        """保存最终结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_file = f"ai_validation_results_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 保存分析报告
        analysis_file = f"ai_validation_analysis_{timestamp}.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 创建CSV报告
        csv_file = f"ai_validation_summary_{timestamp}.csv"
        self.create_csv_report(results, csv_file)
        
        logger.info(f"📊 结果已保存:")
        logger.info(f"   详细结果: {results_file}")
        logger.info(f"   分析报告: {analysis_file}")
        logger.info(f"   CSV摘要: {csv_file}")
    
    def create_csv_report(self, results: List[Dict], filename: str):
        """创建CSV报告"""
        try:
            # 准备CSV数据
            csv_data = []
            for result in results:
                if 'error' not in result:
                    csv_data.append({
                        'case_id': result.get('case_id'),
                        'case_name': result.get('case_name'),
                        'conflict_type': result.get('conflict_type'),
                        'difficulty': result.get('difficulty'),
                        'ai_choice': result.get('ai_consensus_choice'),
                        'agreement_rate': result.get('agreement_rate'),
                        'confidence': result.get('confidence'),
                        'agrees_with_original': result.get('agrees_with_original')
                    })
            
            # 保存CSV
            df = pd.DataFrame(csv_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
        except Exception as e:
            logger.error(f"❌ 创建CSV报告失败: {e}")

def main():
    """主函数"""
    
    print("🚀 AI多模型验证系统")
    print("=" * 50)
    
    # 创建批量验证器
    batch_validator = BatchValidator()
    
    # 加载测试数据
    test_cases = batch_validator.load_test_dataset()
    if not test_cases:
        print("❌ 无法加载测试数据")
        return
    
    # 询问验证范围
    total_cases = len(test_cases)
    print(f"📊 共有 {total_cases} 个测试案例")
    
    try:
        start_idx = int(input(f"请输入开始索引 (0-{total_cases-1}, 默认0): ") or "0")
        batch_size = int(input(f"请输入批量大小 (默认{min(10, total_cases)}): ") or str(min(10, total_cases)))
        delay = int(input("请输入请求间隔秒数 (默认2): ") or "2")
    except ValueError:
        print("❌ 输入无效，使用默认值")
        start_idx, batch_size, delay = 0, min(10, total_cases), 2
    
    # 开始批量验证
    print(f"\n🔄 开始验证案例 {start_idx+1} 到 {min(start_idx+batch_size, total_cases)}")
    
    results = batch_validator.validate_batch(
        test_cases, 
        start_index=start_idx,
        batch_size=batch_size,
        delay_seconds=delay
    )
    
    if not results:
        print("❌ 验证失败，没有结果")
        return
    
    # 分析结果
    print("\n📊 分析验证结果...")
    analysis = batch_validator.analyze_validation_results(results)
    
    # 显示摘要
    if 'summary' in analysis:
        summary = analysis['summary']
        print(f"\n📈 验证摘要:")
        print(f"   总案例数: {summary['total_cases']}")
        print(f"   成功验证: {summary['successful_validations']}")
        print(f"   与原始答案一致: {summary['agreement_with_original']}")
        print(f"   一致率: {summary['overall_agreement_rate']:.2%}")
    
    # 保存结果
    batch_validator.save_results(results, analysis)
    
    print("\n✅ 验证完成！")

if __name__ == "__main__":
    main()
