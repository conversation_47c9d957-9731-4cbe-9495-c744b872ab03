#!/usr/bin/env python3
"""
最终清洁版可视化 - 避免特殊字符问题
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.patches as patches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_clean_final_visualization():
    """创建最终清洁版可视化"""
    
    # 实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景表现数据
    weight_dominant = [1.000, 0.500, 1.000]
    time_dominant = [0.160, 1.000, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.800]
    edge_case = [0.650, 0.350, 0.900]
    
    # 各难度表现数据
    easy_performance = [0.550, 0.400, 1.000]
    medium_performance = [0.722, 0.472, 0.917]
    hard_performance = [0.591, 0.705, 0.682]
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=18, fontweight='bold', y=0.95)
    
    # 高对比度颜色
    colors_main = ['#D32F2F', '#FF8F00', '#2E7D32']
    
    # 1. 整体准确率对比
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors_main, alpha=0.9, 
                    edgecolor='black', linewidth=2)
    ax1.set_title('整体准确率对比', fontsize=14, fontweight='bold', pad=15)
    ax1.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3, axis='y')
    
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 2. 按冲突类型分析
    x = np.arange(len(strategies))
    width = 0.18
    
    colors_scenario = ['#B71C1C', '#E65100', '#1565C0', '#2E7D32']
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', 
           color=colors_scenario[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', 
           color=colors_scenario[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', 
           color=colors_scenario[2], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', 
           color=colors_scenario[3], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=14, fontweight='bold', pad=15)
    ax2.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, fontweight='bold')
    ax2.legend(fontsize=10, frameon=True)
    ax2.set_ylim(0, 1.1)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 按难度等级分析
    colors_difficulty = ['#4CAF50', '#FF9800', '#F44336']
    
    ax3.bar(x - width, easy_performance, width, label='简单', 
           color=colors_difficulty[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x, medium_performance, width, label='中等', 
           color=colors_difficulty[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x + width, hard_performance, width, label='困难', 
           color=colors_difficulty[2], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax3.set_title('按难度等级的准确率分析', fontsize=14, fontweight='bold', pad=15)
    ax3.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies, fontweight='bold')
    ax3.legend(fontsize=10, frameon=True)
    ax3.set_ylim(0, 1.1)
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 策略匹配矩阵
    create_clean_matrix(ax4)
    
    plt.tight_layout()
    plt.savefig('clean_final_results.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

def create_clean_matrix(ax):
    """创建清洁版策略矩阵"""
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 性能数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]
    ])
    
    # 确定最佳策略
    best_strategy_indices = np.argmax(performance_data, axis=0)
    
    # 创建热力图
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1, alpha=0.8)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold', fontsize=10)
    ax.set_yticklabels(strategies, fontweight='bold', fontsize=10)
    
    # 添加网格
    ax.set_xticks(np.arange(len(scenarios)+1)-.5, minor=True)
    ax.set_yticks(np.arange(len(strategies)+1)-.5, minor=True)
    ax.grid(which="minor", color="white", linestyle='-', linewidth=2)
    ax.tick_params(which="minor", size=0)
    
    # 添加数值和标记
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = performance_data[i, j]
            
            # 添加百分比数值
            ax.text(j, i, f'{value:.1%}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=9)
            
            # 为最佳策略添加标记
            if i == best_strategy_indices[j]:
                # 金色边框
                rect = patches.Rectangle((j-0.45, i-0.45), 0.9, 0.9, 
                                       linewidth=3, edgecolor='gold', 
                                       facecolor='none', alpha=1.0)
                ax.add_patch(rect)
                
                # 使用简单的文字标记代替特殊字符
                ax.text(j+0.3, i-0.3, 'BEST', ha="center", va="center", 
                       fontsize=8, color='red', fontweight='bold')
    
    ax.set_title('策略-场景最佳匹配矩阵\n(BEST = 最佳策略)', 
                fontsize=14, fontweight='bold', pad=15)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('准确率', fontweight='bold', fontsize=10)

def create_summary_table():
    """创建结果汇总表"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # 汇总数据
    data = [
        ['策略', '整体准确率', '权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难'],
        ['Weight-First', '63.0%', '100.0%', '16.0%', '64.0%', '65.0%', '55.0%', '72.2%', '59.1%'],
        ['Latest-First', '56.0%', '50.0%', '100.0%', '36.0%', '35.0%', '40.0%', '47.2%', '70.5%'],
        ['自适应Hybrid', '83.0%', '100.0%', '60.0%', '80.0%', '90.0%', '100.0%', '91.7%', '68.2%']
    ]
    
    # 创建表格
    table = ax.table(cellText=data[1:], colLabels=data[0], cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(data[0])):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 突出显示最佳结果
    best_positions = [(1, 0), (1, 2), (2, 3), (3, 1), (3, 4), (3, 5), (3, 6), (3, 7)]
    for pos in best_positions:
        if pos[0] < len(data) and pos[1] < len(data[0]):
            table[pos].set_facecolor('#FFD700')
            table[pos].set_text_props(weight='bold')
    
    ax.set_title('策略性能汇总表\n基于拉萨知识图谱冲突解决实验', 
                fontsize=16, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('strategy_summary_table.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("🎨 创建最终清洁版可视化")
    print("=" * 50)
    
    # 创建主图
    print("📊 创建清洁版四图对比...")
    create_clean_final_visualization()
    print("✅ 清洁版主图已保存: clean_final_results.png")
    
    # 创建汇总表
    print("📊 创建策略性能汇总表...")
    create_summary_table()
    print("✅ 汇总表已保存: strategy_summary_table.png")
    
    print(f"\n🎉 清洁版可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • clean_final_results.png - 清洁版四图对比")
    print(f"   • strategy_summary_table.png - 策略性能汇总表")
    
    print(f"\n✅ 特点:")
    print(f"   • 中文字体正确显示")
    print(f"   • 避免特殊字符问题")
    print(f"   • 高对比度设计")
    print(f"   • 清晰的数据标注")
    print(f"   • 突出最佳策略选择")
    
    print(f"\n📊 关键结果:")
    print(f"   • 自适应Hybrid: 83.0% (最佳整体性能)")
    print(f"   • Weight-First: 63.0% (权重主导场景完美)")
    print(f"   • Latest-First: 56.0% (困难场景表现突出)")

if __name__ == "__main__":
    main()
