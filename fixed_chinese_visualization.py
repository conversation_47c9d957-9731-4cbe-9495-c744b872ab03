#!/usr/bin/env python3
"""
修复中文显示和数据问题的可视化
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.patches as patches

# 修复中文字体问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_fixed_visualization():
    """创建修复后的可视化"""
    
    # 正确的实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景下的表现（基于实际实验结果）
    weight_dominant = [1.000, 0.500, 1.000]  # 30个案例
    time_dominant = [0.160, 1.000, 0.600]    # 25个案例
    complex_tradeoff = [0.640, 0.360, 0.800] # 25个案例
    edge_case = [0.650, 0.350, 0.900]        # 20个案例
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000]    # 20个案例
    medium_performance = [0.722, 0.472, 0.917]  # 36个案例
    hard_performance = [0.591, 0.705, 0.682]    # 44个案例
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=18, fontweight='bold', y=0.95)
    
    # 高对比度颜色
    colors_main = ['#D32F2F', '#FF8F00', '#2E7D32']
    
    # 1. 整体准确率对比
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors_main, alpha=0.9, 
                    edgecolor='black', linewidth=2)
    ax1.set_title('整体准确率对比', fontsize=14, fontweight='bold', pad=15)
    ax1.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 2. 按冲突类型分析
    x = np.arange(len(strategies))
    width = 0.18
    
    colors_scenario = ['#B71C1C', '#E65100', '#1565C0', '#2E7D32']
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', 
           color=colors_scenario[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', 
           color=colors_scenario[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', 
           color=colors_scenario[2], alpha=0.9, edgecolor='black', linewidth=1)
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', 
           color=colors_scenario[3], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=14, fontweight='bold', pad=15)
    ax2.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, fontweight='bold')
    ax2.legend(fontsize=10, frameon=True)
    ax2.set_ylim(0, 1.1)
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 按难度等级分析
    colors_difficulty = ['#4CAF50', '#FF9800', '#F44336']
    
    ax3.bar(x - width, easy_performance, width, label='简单', 
           color=colors_difficulty[0], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x, medium_performance, width, label='中等', 
           color=colors_difficulty[1], alpha=0.9, edgecolor='black', linewidth=1)
    ax3.bar(x + width, hard_performance, width, label='困难', 
           color=colors_difficulty[2], alpha=0.9, edgecolor='black', linewidth=1)
    
    ax3.set_title('按难度等级的准确率分析', fontsize=14, fontweight='bold', pad=15)
    ax3.set_ylabel('准确率', fontsize=12, fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies, fontweight='bold')
    ax3.legend(fontsize=10, frameon=True)
    ax3.set_ylim(0, 1.1)
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 策略匹配矩阵
    create_strategy_matrix(ax4)
    
    plt.tight_layout()
    plt.savefig('fixed_chinese_results.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

def create_strategy_matrix(ax):
    """创建策略匹配矩阵"""
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 正确的性能数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],  # Weight-First
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],  # Latest-First
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]   # 自适应Hybrid
    ])
    
    # 确定每个场景的最佳策略
    best_strategy_indices = np.argmax(performance_data, axis=0)
    
    # 创建热力图
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1, alpha=0.8)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold', fontsize=10)
    ax.set_yticklabels(strategies, fontweight='bold', fontsize=10)
    
    # 添加网格
    ax.set_xticks(np.arange(len(scenarios)+1)-.5, minor=True)
    ax.set_yticks(np.arange(len(strategies)+1)-.5, minor=True)
    ax.grid(which="minor", color="white", linestyle='-', linewidth=2)
    ax.tick_params(which="minor", size=0)
    
    # 添加数值和最佳策略标记
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = performance_data[i, j]
            
            # 添加百分比数值
            ax.text(j, i, f'{value:.1%}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=9)
            
            # 为最佳策略添加金色边框
            if i == best_strategy_indices[j]:
                rect = patches.Rectangle((j-0.45, i-0.45), 0.9, 0.9, 
                                       linewidth=3, edgecolor='gold', 
                                       facecolor='none', alpha=1.0)
                ax.add_patch(rect)
                
                # 添加皇冠标记
                ax.text(j+0.3, i-0.3, '★', ha="center", va="center", 
                       fontsize=12, color='gold', fontweight='bold')
    
    ax.set_title('策略-场景最佳匹配矩阵\n(★ = 最佳策略)', 
                fontsize=14, fontweight='bold', pad=15)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('准确率', fontweight='bold', fontsize=10)

def create_standalone_matrix():
    """创建独立的策略匹配矩阵"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单场景', '中等场景', '困难场景']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 正确的性能数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],  # Weight-First
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],  # Latest-First
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]   # 自适应Hybrid
    ])
    
    # 确定每个场景的最佳策略
    best_strategy_indices = np.argmax(performance_data, axis=0)
    
    # 创建热力图
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold', fontsize=12)
    ax.set_yticklabels(strategies, fontweight='bold', fontsize=12)
    
    # 添加网格
    ax.set_xticks(np.arange(len(scenarios)+1)-.5, minor=True)
    ax.set_yticks(np.arange(len(strategies)+1)-.5, minor=True)
    ax.grid(which="minor", color="white", linestyle='-', linewidth=3)
    ax.tick_params(which="minor", size=0)
    
    # 添加数值和最佳策略标记
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = performance_data[i, j]
            
            # 添加百分比数值
            ax.text(j, i, f'{value:.1%}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=11)
            
            # 为最佳策略添加特殊标记
            if i == best_strategy_indices[j]:
                # 金色边框
                rect = patches.Rectangle((j-0.48, i-0.48), 0.96, 0.96, 
                                       linewidth=4, edgecolor='gold', 
                                       facecolor='none', alpha=1.0)
                ax.add_patch(rect)
                
                # 皇冠和星星标记
                ax.text(j+0.35, i-0.35, '👑', ha="center", va="center", fontsize=16)
                ax.text(j-0.35, i+0.35, '⭐', ha="center", va="center", fontsize=14)
    
    ax.set_title('策略-场景最佳匹配矩阵\n基于拉萨知识图谱冲突解决实验', 
                fontsize=16, fontweight='bold', pad=25)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('准确率', fontweight='bold', fontsize=12)
    
    # 添加图例说明
    legend_text = """图例说明：
• 颜色：绿色=高准确率，红色=低准确率
• 👑：该场景下的最佳策略
• ⭐：性能优异标记
• 金框：突出最佳选择

关键发现：
• 自适应Hybrid在5/7场景中表现最佳
• Latest-First在困难场景表现突出(70.5%)
• Weight-First在权重主导场景完美(100%)"""
    
    ax.text(1.15, 0.5, legend_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", 
           facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('fixed_standalone_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("🔧 修复中文显示和数据问题...")
    print("=" * 50)
    
    # 创建修复后的主图
    print("📊 创建修复后的四图对比...")
    create_fixed_visualization()
    print("✅ 修复后主图已保存: fixed_chinese_results.png")
    
    # 创建修复后的独立矩阵
    print("📊 创建修复后的独立矩阵...")
    create_standalone_matrix()
    print("✅ 修复后独立矩阵已保存: fixed_standalone_matrix.png")
    
    print(f"\n🎉 修复完成！")
    print(f"📁 修复后的文件:")
    print(f"   • fixed_chinese_results.png - 修复中文和数据的四图对比")
    print(f"   • fixed_standalone_matrix.png - 修复后的独立策略矩阵")
    
    print(f"\n✅ 修复内容:")
    print(f"   • 中文字体正确显示")
    print(f"   • 使用正确的实验数据")
    print(f"   • 清晰的百分比标注")
    print(f"   • 突出的最佳策略标记")

if __name__ == "__main__":
    main()
