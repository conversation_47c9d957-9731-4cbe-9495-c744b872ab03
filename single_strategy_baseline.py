#!/usr/bin/env python3
"""
单策略基准系统

实现传统的单策略知识图谱更新方法，作为对比基准。
特点：
1. 仅基于简单规则或固定模板
2. 不进行复杂的冲突解决
3. 不使用LLM推理
4. 不进行实体消歧
5. 简单的关系推断
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from neo4j_connection import Neo4jConnection
from neo4j_crud import Neo4jCRUD

logger = logging.getLogger(__name__)

@dataclass
class SingleStrategyResult:
    """单策略处理结果"""
    processed: int = 0
    failed: int = 0
    relationships_created: int = 0
    conflicts_detected: int = 0
    conflicts_resolved: int = 0
    processing_time: float = 0.0
    details: Dict[str, Any] = None

class SingleStrategyProcessor:
    """单策略处理器"""
    
    def __init__(self, neo4j_conn: Neo4jConnection):
        self.neo4j_conn = neo4j_conn
        self.crud = Neo4jCRUD(neo4j_conn)
        
        # 简单的规则配置
        self.rules = {
            "min_visitor_percentage": 0.0,  # 最小访客比例
            "required_fields": ["name"],     # 必需字段
            "location_mapping": {            # 位置映射规则
                "拉萨": "拉萨市",
                "日喀则": "日喀则市",
                "林芝": "林芝市",
                "昌都": "昌都市",
                "山南": "山南市",
                "那曲": "那曲市",
                "阿里": "阿里地区"
            },
            "category_mapping": {            # 类别映射规则
                "寺庙": "宗教建筑",
                "寺院": "宗教建筑",
                "宫殿": "历史建筑",
                "湖泊": "自然风光",
                "山峰": "自然风光",
                "街道": "文化街区"
            }
        }
        
        logger.info("单策略处理器初始化完成")
    
    async def process_data(self, data: List[Dict], source_type: str = "unknown") -> SingleStrategyResult:
        """处理数据的主要方法"""
        start_time = time.time()
        result = SingleStrategyResult()
        
        try:
            # 第一阶段：数据预处理和验证
            validated_data = self._validate_and_preprocess(data)
            logger.info(f"数据验证完成，有效数据: {len(validated_data)} 条")
            
            # 第二阶段：创建实体
            entity_results = await self._create_entities(validated_data, source_type)
            result.processed = entity_results["created"]
            result.failed = entity_results["failed"]
            
            # 第三阶段：创建简单关系
            relationship_results = await self._create_simple_relationships(validated_data)
            result.relationships_created = relationship_results["created"]
            
            # 第四阶段：简单冲突检测（但不解决）
            conflict_results = await self._detect_simple_conflicts(validated_data)
            result.conflicts_detected = conflict_results["detected"]
            result.conflicts_resolved = 0  # 单策略不解决冲突
            
            result.processing_time = time.time() - start_time
            result.details = {
                "entity_results": entity_results,
                "relationship_results": relationship_results,
                "conflict_results": conflict_results
            }
            
            logger.info(f"单策略处理完成: 处理 {result.processed} 个实体, "
                       f"创建 {result.relationships_created} 个关系, "
                       f"检测到 {result.conflicts_detected} 个冲突")
            
        except Exception as e:
            logger.error(f"单策略处理失败: {e}")
            result.processing_time = time.time() - start_time
            raise
        
        return result
    
    def _validate_and_preprocess(self, data: List[Dict]) -> List[Dict]:
        """数据验证和预处理"""
        validated_data = []
        
        for item in data:
            # 检查必需字段
            if not all(item.get(field) for field in self.rules["required_fields"]):
                continue
            
            # 检查访客比例
            visitor_pct = self._parse_visitor_percentage(item.get("visitor_percentage", "0%"))
            if visitor_pct < self.rules["min_visitor_percentage"]:
                continue
            
            # 标准化数据
            processed_item = self._standardize_item(item)
            validated_data.append(processed_item)
        
        return validated_data
    
    def _parse_visitor_percentage(self, value: str) -> float:
        """解析访客比例"""
        try:
            if isinstance(value, str):
                value = value.strip('%')
            return float(value) if value else 0.0
        except (ValueError, TypeError):
            return 0.0
    
    def _standardize_item(self, item: Dict) -> Dict:
        """标准化数据项"""
        standardized = item.copy()
        
        # 标准化位置
        location = item.get("location", "")
        for key, value in self.rules["location_mapping"].items():
            if key in location:
                standardized["location"] = value
                break
        
        # 标准化类别
        category = item.get("category", "")
        for key, value in self.rules["category_mapping"].items():
            if key in category:
                standardized["category"] = value
                break
        
        # 添加处理时间戳
        standardized["processed_at"] = datetime.now().isoformat()
        standardized["strategy"] = "single"
        
        return standardized
    
    async def _create_entities(self, data: List[Dict], source_type: str) -> Dict[str, int]:
        """创建实体"""
        created = 0
        failed = 0
        
        with self.neo4j_conn.driver.session() as session:
            for item in data:
                try:
                    # 简单的实体创建，不检查重复
                    def create_entity_tx(tx):
                        query = """
                        CREATE (a:Attraction {
                            name: $name,
                            category: $category,
                            location: $location,
                            description: $description,
                            visitor_percentage: $visitor_percentage,
                            ranking: $ranking,
                            source_type: $source_type,
                            processed_at: $processed_at,
                            strategy: $strategy,
                            pub_timestamp: $pub_timestamp
                        })
                        RETURN a.name as name
                        """
                        
                        params = {
                            "name": item.get("name"),
                            "category": item.get("category", "未分类"),
                            "location": item.get("location", "未知"),
                            "description": item.get("description", ""),
                            "visitor_percentage": item.get("visitor_percentage", "0%"),
                            "ranking": item.get("ranking", 999),
                            "source_type": source_type,
                            "processed_at": item.get("processed_at"),
                            "strategy": item.get("strategy"),
                            "pub_timestamp": item.get("pub_timestamp", datetime.now().isoformat())
                        }
                        
                        result = tx.run(query, **params)
                        return result.single()
                    
                    result = session.execute_write(create_entity_tx)
                    if result:
                        created += 1
                        logger.debug(f"创建实体: {item.get('name')}")
                    else:
                        failed += 1
                        
                except Exception as e:
                    logger.error(f"创建实体失败 {item.get('name')}: {e}")
                    failed += 1
        
        return {"created": created, "failed": failed}
    
    async def _create_simple_relationships(self, data: List[Dict]) -> Dict[str, int]:
        """创建简单关系"""
        created = 0
        
        with self.neo4j_conn.driver.session() as session:
            # 只创建基本的 LOCATED_IN 关系
            for item in data:
                try:
                    def create_location_relationship_tx(tx):
                        # 首先确保城市节点存在
                        city_query = """
                        MERGE (c:City {name: $city_name})
                        SET c.type = 'City'
                        RETURN c.name as name
                        """
                        tx.run(city_query, city_name=item.get("location"))
                        
                        # 创建 LOCATED_IN 关系
                        rel_query = """
                        MATCH (a:Attraction {name: $attraction_name})
                        MATCH (c:City {name: $city_name})
                        MERGE (a)-[r:LOCATED_IN]->(c)
                        SET r.created_at = datetime(),
                            r.strategy = 'single'
                        RETURN r
                        """
                        
                        result = tx.run(rel_query, 
                                      attraction_name=item.get("name"),
                                      city_name=item.get("location"))
                        return result.single()
                    
                    result = session.execute_write(create_location_relationship_tx)
                    if result:
                        created += 1
                        logger.debug(f"创建关系: {item.get('name')} -> {item.get('location')}")
                        
                except Exception as e:
                    logger.error(f"创建关系失败 {item.get('name')}: {e}")
        
        return {"created": created}
    
    async def _detect_simple_conflicts(self, data: List[Dict]) -> Dict[str, int]:
        """简单冲突检测（不解决）"""
        detected = 0
        conflicts = []
        
        # 检查重复名称
        name_counts = {}
        for item in data:
            name = item.get("name")
            if name in name_counts:
                name_counts[name] += 1
            else:
                name_counts[name] = 1
        
        # 记录重复项
        for name, count in name_counts.items():
            if count > 1:
                detected += 1
                conflicts.append({
                    "type": "duplicate_name",
                    "entity": name,
                    "count": count,
                    "detected_at": datetime.now().isoformat()
                })
                logger.warning(f"检测到重复实体: {name} (出现 {count} 次)")
        
        # 保存冲突记录但不解决
        if conflicts:
            try:
                with open("single_strategy_conflicts.json", "w", encoding="utf-8") as f:
                    json.dump(conflicts, f, ensure_ascii=False, indent=2)
                logger.info(f"冲突记录已保存，共检测到 {detected} 个冲突")
            except Exception as e:
                logger.error(f"保存冲突记录失败: {e}")
        
        return {"detected": detected, "conflicts": conflicts}
    
    def clear_database(self):
        """清空数据库"""
        try:
            with self.neo4j_conn.driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with self.neo4j_conn.driver.session() as session:
                # 获取实体统计
                entity_result = session.run("""
                    MATCH (a:Attraction) 
                    WHERE a.strategy = 'single'
                    RETURN count(a) as entity_count
                """)
                entity_count = entity_result.single()["entity_count"]
                
                # 获取关系统计
                rel_result = session.run("""
                    MATCH ()-[r]->() 
                    WHERE r.strategy = 'single'
                    RETURN count(r) as rel_count
                """)
                rel_count = rel_result.single()["rel_count"]
                
                return {
                    "entities": entity_count,
                    "relationships": rel_count,
                    "strategy": "single"
                }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"entities": 0, "relationships": 0, "strategy": "single"}

# 简单的关系推断规则
class SimpleRelationshipInference:
    """简单关系推断"""
    
    @staticmethod
    def infer_relationships(entities: List[Dict]) -> List[Dict]:
        """基于简单规则推断关系"""
        relationships = []
        
        # 规则1：同一城市的景点之间可能有NEARBY关系
        location_groups = {}
        for entity in entities:
            location = entity.get("location")
            if location:
                if location not in location_groups:
                    location_groups[location] = []
                location_groups[location].append(entity)
        
        # 为同一城市的景点创建NEARBY关系
        for location, group in location_groups.items():
            if len(group) > 1:
                for i, entity1 in enumerate(group):
                    for entity2 in group[i+1:]:
                        relationships.append({
                            "source": entity1.get("name"),
                            "target": entity2.get("name"),
                            "type": "NEARBY",
                            "properties": {
                                "reason": f"同在{location}",
                                "confidence": 0.6,
                                "inferred_by": "simple_rule"
                            }
                        })
        
        # 规则2：相同类别的景点之间有SIMILAR_TYPE关系
        category_groups = {}
        for entity in entities:
            category = entity.get("category")
            if category:
                if category not in category_groups:
                    category_groups[category] = []
                category_groups[category].append(entity)
        
        # 为相同类别的景点创建SIMILAR_TYPE关系（限制数量避免过多）
        for category, group in category_groups.items():
            if len(group) > 1 and len(group) <= 5:  # 限制组大小
                for i, entity1 in enumerate(group):
                    for entity2 in group[i+1:]:
                        relationships.append({
                            "source": entity1.get("name"),
                            "target": entity2.get("name"),
                            "type": "SIMILAR_TYPE",
                            "properties": {
                                "reason": f"同属{category}",
                                "confidence": 0.7,
                                "inferred_by": "simple_rule"
                            }
                        })
        
        return relationships
