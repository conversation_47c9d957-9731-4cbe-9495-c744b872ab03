#!/usr/bin/env python3
"""
运行知识图谱演化质量评估实验

这是实验的主入口脚本，用于运行完整的对比实验。
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from neo4j_connection import Neo4jConnection
from evolution_quality_experiment import EvolutionQualityExperiment

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('experiment.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('neo4j').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

async def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("="*80)
    print("知识图谱演化质量评估实验")
    print("="*80)
    print("实验目标：验证多策略代理在知识图谱动态演化中的准确性和完整性")
    print("对比方案：多策略代理 vs 传统单策略")
    print("评估指标：准确率、覆盖率、冲突解决率、演化效率")
    print("="*80)
    
    neo4j_conn = None
    
    try:
        # 1. 初始化配置和连接
        print("\n🔧 初始化系统...")
        config = Config()
        
        # 验证配置
        if not config.validate_config():
            raise Exception("配置验证失败，请检查 Neo4j 连接和 API 密钥设置")
        
        # 连接Neo4j
        neo4j_config = config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            neo4j_config["uri"],
            neo4j_config["user"],
            neo4j_config["password"]
        )
        
        neo4j_conn.verify_connectivity()  # 这会抛出异常如果连接失败
        
        print("✅ 系统初始化完成")
        
        # 2. 创建实验实例
        print("\n🧪 准备实验环境...")
        experiment = EvolutionQualityExperiment(neo4j_conn)
        print("✅ 实验环境准备完成")
        
        # 3. 运行实验
        print("\n🚀 开始运行实验...")
        print("注意：实验将分别运行多策略代理和单策略基准系统")
        print("预计耗时：3-5分钟")
        
        results = await experiment.run_complete_experiment()
        
        # 4. 显示结果摘要
        print("\n" + "="*80)
        print("📊 实验结果摘要")
        print("="*80)
        
        for result in results:
            print(f"\n策略: {result.strategy_name}")
            print("-" * 40)
            print(f"准确率:     {result.metrics.accuracy:.3f}")
            print(f"覆盖率:     {result.metrics.coverage:.3f}")
            print(f"冲突解决率: {result.metrics.conflict_resolution_rate:.3f}")
            print(f"F1分数:     {result.metrics.f1_score:.3f}")
            print(f"演化效率:   {result.metrics.evolution_efficiency:.2f} 实体/秒")
            print(f"处理时间:   {result.metrics.processing_time:.2f} 秒")
            print(f"创建实体:   {result.metrics.entities_created}")
            print(f"创建关系:   {result.metrics.relationships_created}")
            print(f"检测冲突:   {result.metrics.conflicts_detected}")
            print(f"解决冲突:   {result.metrics.conflicts_resolved}")
        
        # 5. 对比分析
        if len(results) >= 2:
            multi_strategy = next((r for r in results if "多策略" in r.strategy_name), None)
            single_strategy = next((r for r in results if "单策略" in r.strategy_name), None)
            
            if multi_strategy and single_strategy:
                print("\n" + "="*80)
                print("📈 对比分析")
                print("="*80)
                
                accuracy_improvement = (multi_strategy.metrics.accuracy - single_strategy.metrics.accuracy) * 100
                coverage_improvement = (multi_strategy.metrics.coverage - single_strategy.metrics.coverage) * 100
                conflict_improvement = (multi_strategy.metrics.conflict_resolution_rate - single_strategy.metrics.conflict_resolution_rate) * 100
                
                print(f"准确率提升:     {accuracy_improvement:+.1f}%")
                print(f"覆盖率提升:     {coverage_improvement:+.1f}%")
                print(f"冲突解决率提升: {conflict_improvement:+.1f}%")
                
                if multi_strategy.metrics.f1_score > single_strategy.metrics.f1_score:
                    print("✅ 多策略代理在综合性能上优于单策略基准")
                else:
                    print("⚠️  单策略基准在某些指标上表现更好")
        
        # 6. 文件输出提示
        print("\n" + "="*80)
        print("📁 输出文件")
        print("="*80)
        print("实验结果:     experiment_results.json")
        print("可视化图表:   experiment_visualization.png")
        print("实验日志:     experiment.log")
        print("冲突报告:     conflict_report.json (如果有)")
        
        print("\n🎉 实验完成！")
        print("请查看生成的可视化图表和详细报告。")
        
    except KeyboardInterrupt:
        print("\n⚠️  实验被用户中断")
        logger.info("实验被用户中断")
    except Exception as e:
        print(f"\n❌ 实验执行失败: {e}")
        logger.error(f"实验执行失败: {e}", exc_info=True)
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        print("1. 检查 Neo4j 数据库是否正在运行")
        print("2. 验证 config.py 中的配置是否正确")
        print("3. 确认 DEEPSEEK_API_KEY 环境变量已设置")
        print("4. 查看 experiment.log 获取详细错误信息")
        
        return 1
    
    finally:
        if neo4j_conn:
            neo4j_conn.close()
            print("🔌 数据库连接已关闭")
    
    return 0

def print_system_info():
    """打印系统信息"""
    print("\n📋 系统信息:")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")

if __name__ == "__main__":
    print_system_info()
    
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n实验被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n启动失败: {e}")
        sys.exit(1)
