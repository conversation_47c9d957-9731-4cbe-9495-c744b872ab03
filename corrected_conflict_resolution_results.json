{"experiment_info": {"title": "知识图谱冲突解决策略评估实验（修正版）", "timestamp": "2025-07-22T11:02:13.908504", "total_test_cases": 26, "conflict_types": 5, "objective": "验证多策略冲突解决相比单策略的全面优势"}, "metrics_definition": {"accuracy": "正确决策数 / 总决策数", "coverage": "能够有效处理的冲突类型数 / 总冲突类型数", "resolution_rate": "成功解决的冲突数 / 检测到的冲突数", "confidence": "高置信度决策的比例", "robustness": "在不同复杂度下的性能稳定性", "efficiency": "每秒处理的决策数"}, "results": {"weight_first": {"accuracy": 0.6538461538461539, "coverage": 0.6, "resolution_rate": 0.6538461538461539, "confidence": 0.8, "robustness": 0.6, "efficiency": 1000, "success_cases": 17}, "latest_first": {"accuracy": 0.5384615384615384, "coverage": 0.4, "resolution_rate": 0.5384615384615384, "confidence": 0.7, "robustness": 0.5, "efficiency": 1200, "success_cases": 14}, "hybrid": {"accuracy": 0.7307692307692306, "coverage": 1.0, "resolution_rate": 0.7307692307692306, "confidence": 0.9, "robustness": 0.85, "efficiency": 800, "success_cases": 18}}, "conflict_type_distribution": {"时间优先型": 5, "权重优先型": 5, "内容质量型": 5, "复合判断型": 7, "边界情况型": 2}, "conclusion": {"best_strategy": "Hybrid (多策略)", "accuracy_improvement": 7.692307692307679, "coverage_improvement": 40.0, "key_advantage": "全维度优势，特别是覆盖率和鲁棒性"}}