#负责文本处理和 LLM 管道逻辑
from datetime import datetime
import json
import logging
import os
from pathlib import Path
import asyncio
import aiohttp
import httpx
from typing import Dict, List, Union
from httpx import AsyncClient
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from time_converter import convert_to_beijing_time
from neo4j_connection import Neo4jConnection
from config import Config
from utils import normalize_location
from conflict_resolution import ConflictResolver
from enhanced_conflict_resolution import EnhancedConflictResolver, EntityDisambiguation
from knowledge_graph_updater import KnowledgeGraphUpdater

logger = logging.getLogger(__name__)

http_client = AsyncClient(timeout=30.0)

PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))

llm_log_file = os.path.join(PROJECT_DIR, "llm_response_log.json")
with open(llm_log_file, "a", encoding="utf-8") as f:
    f.write("")

request_semaphore = asyncio.Semaphore(10)

# Cache for LLM descriptions
description_cache = {}

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
    before_sleep=lambda retry_state: logger.debug(f"重试 LLM 调用，第 {retry_state.attempt_number} 次")
)
async def call_deepseek_with_retry(prompt: str, model_name: str = "default") -> str:
    config = Config.get_llm_config(model_name)
    input_tokens = estimate_tokens(prompt)
    max_model_tokens = 16384  # 调整为实际模型限制（如 4096）
    max_output_tokens = 1024
    if input_tokens >= max_model_tokens - max_output_tokens:
        logger.warning(f"Prompt 过长 ({input_tokens} tokens)，截断到安全长度")
        prompt = prompt[:int(len(prompt) * (max_model_tokens - max_output_tokens - 100) / input_tokens)]
        input_tokens = estimate_tokens(prompt)
    
    logger.debug(f"输入 token 数: {input_tokens}, max_tokens: {max_output_tokens}")
    
    api_key = config.get("api_key", "")
    if not api_key:
        logger.error("API密钥为空，无法发起请求")
        raise ValueError("API密钥为空")
    
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    async with request_semaphore:
        async with AsyncClient(timeout=config["timeout"]) as client:
            try:
                response = await client.post(
                    config["api_base"],
                    json={
                        "model": config["model"],
                        "messages": [{"role": "user", "content": prompt}],
                        "max_tokens": max_output_tokens
                    },
                    headers=headers
                )
                response.raise_for_status()
                response_data = response.json()
                with open(llm_log_file, "a", encoding="utf-8") as f:
                    json.dump(
                        {
                            "prompt": prompt,
                            "response": response_data,
                            "input_tokens": input_tokens,
                            "timestamp": await convert_to_beijing_time()
                        },
                        f,
                        ensure_ascii=False
                    )
                    f.write("\n")
                return json.dumps(response_data)
            except httpx.HTTPStatusError as e:
                logger.error(f"LLM 调用失败: {e}, Response: {e.response.text}")
                raise
            except Exception as e:
                logger.error(f"LLM 调用失败: {e}")
                raise

#用于估算token，保证不超令牌数
def estimate_tokens(text: str) -> int:
    """估算文本的 token 数（简化为 1 token ≈ 0.75 个中文字符或 0.5 个英文字符）"""
    chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
    other_chars = len(text) - chinese_chars
    return int(chinese_chars * 0.75 + other_chars * 0.5)

async def infer_description(name: str, location: str) -> str:
    """使用 LLM 生成描述"""
    prompt = f"Generate a brief description (50-100 words) for a tourist attraction named '{name}' located in '{location}'. The description should highlight its cultural, historical, or natural significance."
    try:
        response = await call_deepseek_with_retry(prompt, model_name="default")
        description = json.loads(response).get("choices", [{}])[0].get("message", {}).get("content", "")
        return description.strip()
    except Exception as e:
        logger.error(f"生成描述失败 for {name}: {e}")
        return f"Description for {name}"

async def reset_database(neo4j_conn):
    """
    Reset the Neo4j database by clearing all nodes and relationships
    and creating indexes for Attraction and City nodes.
    """
    try:
        neo4j_conn.clear_database()
        logger.info("清空所有节点和关系")

        with neo4j_conn.driver.session() as session:
            session.run("CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)")
            logger.debug("执行查询: CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)")
            
            session.run("CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)")
            logger.debug("执行查询: CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)")
        
        logger.info("创建 Attraction 和 City 名称索引")
    except Exception as e:
        logger.error(f"重置数据库失败: {e}", exc_info=True)
        raise

async def extract_best_comment(description: str, debug_mode: bool = False) -> str:
    """使用 LLM 提取最佳评论"""
    if debug_mode:
        return f"Sample comment for {description[:20]}..."
    prompt = f"Generate a concise, positive visitor comment (20-50 words) for a tourist attraction based on this description: {description}"
    try:
        response = await call_deepseek_with_retry(prompt, model_name="default")
        comment = json.loads(response).get("choices", [{}])[0].get("message", {}).get("content", "")
        return comment.strip()
    except Exception as e:
        logger.error(f"生成评论失败: {e}")
        return ""

# text_processor.py
async def batch_call_deepseek(prompts: List[str]) -> List[Union[str, Exception]]:
    batch_size = 10  # 增大批处理大小
    semaphore = asyncio.Semaphore(20)  # 增大并发
    results = []
    async with aiohttp.ClientSession() as session:
        for i in range(0, len(prompts), batch_size):
            batch = prompts[i:i + batch_size]
            tasks = []
            for prompt in batch:
                async def call_llm(p):
                    async with semaphore:
                        try:
                            async with session.post(
                                Config.get_llm_config()['api_base'],
                                json={
                                    "model": Config.get_llm_config()['model'],
                                    "messages": [{"role": "user", "content": p}],
                                    "max_tokens": 2000
                                },
                                headers={"Authorization": f"Bearer {Config.get_llm_config()['api_key']}"}
                            ) as response:
                                if response.status != 200:
                                    raise Exception(f"HTTP {response.status}")
                                result = await response.json()
                                logger.debug(f"LLM 响应: {result}")
                                return json.dumps(result)
                        except Exception as e:
                            logger.error(f"LLM 调用失败: {e}")
                            return e
                tasks.append(call_llm(prompt))
            results.extend(await asyncio.gather(*tasks))
    return results

async def extract_relationships(data: Dict) -> Dict:
    """增强数据，生成描述和评论"""
    data = data.copy()
    if not data.get("best_comment"):
        data["best_comment"] = await extract_best_comment(data.get("description", ""), debug_mode=False)
    if not data.get("location"):
        data["location"] = "拉萨市"
    if not data.get("description"):
        data["description"] = await infer_description(data.get("name", "Unknown"), data.get("location", "拉萨市"))
    
    cultural_keywords = ["寺", "庙", "宫", "博物馆", "文化", "宗教", "历史", "古街", "遗址", "纪念碑"]
    is_cultural = any(keyword in data.get("name", "") or keyword in data.get("description", "") for keyword in cultural_keywords)
    data["is_cultural"] = is_cultural
    logger.debug(f"LLM 处理数据: {data['name']}, is_cultural: {is_cultural}, location: {data['location']}, description: {data['description']}")
    return data

async def process_json_files(neo4j_conn, json_file_path: str, crawl_timestamp: str, source_type: str, metrics: Dict):
    logger.info(f"读取 JSON 文件: {json_file_path}")
    try:
        file_path = Path(json_file_path)
        if file_path.is_dir():
            raise ValueError(f"路径是目录而非文件: {json_file_path}")
        if not file_path.is_file():
            raise FileNotFoundError(f"文件不存在: {json_file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        nodes = data.get("nodes", [])
        logger.info(f"JSON 文件包含 {len(nodes)} 条记录")
        
        nodes = nodes[:100]  # 限制测试节点数
        logger.info(f"限制测试节点数为: {len(nodes)}")

        seen_names = set()
        duplicates = []
        unique_nodes = []
        for node in nodes:
            if node["name"] in seen_names:
                duplicates.append(node["name"])
            else:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}, 重复节点: {duplicates if duplicates else '无'}")

        batch_size = 20
        results = []
        for i in range(0, len(unique_nodes), batch_size):
            batch = unique_nodes[i:i + batch_size]
            batch_result = await process_json_chunk(
                neo4j_conn=neo4j_conn,
                data=batch,
                crawl_timestamp=crawl_timestamp,
                source_type=source_type,
                metrics=metrics
            )
            results.append(batch_result)
        
        total_processed = sum(r["processed"] for r in results)
        total_failed = sum(r["failed"] for r in results)
        total_rels = sum(r["relationships_created"] for r in results)
        
        return {
            "status": "success",
            "processed": total_processed,
            "failed": total_failed,
            "relationships_created": total_rels,
            "details": results
        }
    except Exception as e:
        logger.error(f"处理 JSON 文件失败: {e}", exc_info=True)
        return {"status": "failed", "error": str(e)}

async def process_json_chunk(neo4j_conn, data: List[Dict], crawl_timestamp: str, source_type: str, metrics: Dict, use_enhanced_resolver: bool = True):
    logger.info("text_processor.py version: 2025-07-22-v7, Enhanced Conflict Resolution")
    updater = KnowledgeGraphUpdater(neo4j_conn)

    # 选择冲突解决器
    if use_enhanced_resolver:
        conflict_resolver = EnhancedConflictResolver(updater.crud, enable_llm_reasoning=True)
        entity_disambiguator = EntityDisambiguation(updater.crud)
        logger.info("使用增强冲突解决器")
    else:
        conflict_resolver = ConflictResolver(updater.crud)
        entity_disambiguator = None
        logger.info("使用标准冲突解决器")

    results = []

    # Process nodes
    nodes = data.get("nodes", data) if isinstance(data, dict) else data
    for item in nodes:
        try:
            processed_item = await extract_relationships(item)
            log_id = f"{processed_item['name']}_{crawl_timestamp}"
            weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
            reason = "Initial import from JSON with LLM enhancement"
            updater.update_knowledge_graph(processed_item, log_id, reason, weights)
            logger.info(f"成功处理实体: {processed_item['name']}")
            results.append({"name": processed_item["name"], "status": "success"})
        except Exception as e:
            logger.error(f"处理实体 {item.get('name', 'Unknown')} 失败: {e}", exc_info=True)
            results.append({"name": item.get("name", "Unknown"), "status": "failed", "error": str(e)})

    # Filter nodes: only visitor_percentage > 0%
    filtered_data = []
    for item in nodes:
        try:
            vp = item.get("visitor_percentage", "0%")
            # Handle different formats: "80%", "0.8", "80"
            if isinstance(vp, str):
                vp = vp.strip("%")
            vp_float = float(vp) if vp else 0.0
            if vp_float > 0:
                filtered_data.append(item)
                logger.debug(f"通过过滤: {item.get('name')}, Location: {item.get('location')}, Visitor Percentage: {item.get('visitor_percentage')}")
            else:
                logger.debug(f"未通过过滤: {item.get('name')}, Location: {item.get('location')}, Visitor Percentage: {item.get('visitor_percentage')}")
        except (ValueError, TypeError) as e:
            logger.warning(f"无效的 visitor_percentage: {item.get('name')}, 值: {item.get('visitor_percentage')}, 错误: {e}")
            continue

    logger.info(f"过滤后景点数量: {len(filtered_data)}")
    if not filtered_data:
        logger.warning("过滤后无节点，检查 visitor_percentage 字段")
        for item in nodes[:5]:
            logger.warning(f"节点: {item.get('name')}, Location: {item.get('location')}, Visitor Percentage: {item.get('visitor_percentage')}, Description: {item.get('description', '')[:50]}...")

    # Generate node pairs
    relationship_prompts = []
    node_pairs = []
    max_pairs = 2000
    pair_count = 0
    unique_names = set()
    for i, node1 in enumerate(filtered_data):
        name1 = node1.get("name")
        if not name1 or name1 in unique_names:
            logger.warning(f"跳过重复或无效名称: {name1}")
            continue
        unique_names.add(name1)
        for node2 in filtered_data[i+1:]:
            name2 = node2.get("name")
            if not name2 or name2 in unique_names or name1 == name2:
                logger.warning(f"跳过重复或无效名称对: {name1} -> {name2}")
                continue
            if pair_count >= max_pairs:
                logger.info(f"达到最大节点对限制 ({max_pairs})，停止生成")
                break
            desc1 = node1.get("description") or f"{name1} is a tourist attraction in {node1.get('location')}."
            desc2 = node2.get("description") or f"{name2} is a tourist attraction in {node2.get('location')}."
            prompt = f"""
            You are an expert in analyzing relationships between tourist attractions. Given the following two attractions, infer possible relationships based on their attributes. Return a JSON list of relationships, each with 'type' (e.g., NEARBY, SIMILAR_TYPE, COMPLEMENTARY_VISIT, HISTORICAL_LINK), 'reason', and 'confidence' (0 to 1). Criteria:
            - NEARBY: Same location and similar visitor percentage (difference < 20%, if available).
            - SIMILAR_TYPE: Similar descriptions (e.g., both are temples or museums).
            - COMPLEMENTARY_VISIT: Attractions that complement each other (e.g., a temple and a cultural street).
            - HISTORICAL_LINK: Shared historical or cultural significance (e.g., both related to Tibetan Buddhism).
            - Always return at least one relationship (e.g., COMPLEMENTARY_VISIT) for attractions with valid attributes.

            Attraction 1:
            Name: {name1}
            Location: {node1.get('location', '')}
            Description: {desc1}
            Ranking: {node1.get('ranking', '')}
            Visitor Percentage: {node1.get('visitor_percentage', '')}

            Attraction 2:
            Name: {name2}
            Location: {node2.get('location', '')}
            Description: {desc2}
            Ranking: {node2.get('ranking', '')}
            Visitor Percentage: {node2.get('visitor_percentage', '')}

            Return a JSON list in strict format, enclosed in square brackets, with no additional text, comments, or markdown:
            [
                {{"type": "NEARBY", "reason": "Same location", "confidence": 0.9}},
                {{"type": "SIMILAR_TYPE", "reason": "Both are temples", "confidence": 0.8}}
            ]
            If no specific relationships are inferred, return:
            [
                {{"type": "COMPLEMENTARY_VISIT", "reason": "Diverse experiences", "confidence": 0.7}}
            ]
            """
            relationship_prompts.append(prompt)
            node_pairs.append((node1, node2))
            pair_count += 1
        if pair_count >= max_pairs:
            break

    logger.info(f"生成 {pair_count} 个节点对进行关系推断")
    if not node_pairs:
        logger.warning("未生成节点对，检查过滤后的节点数量和名称唯一性")

    # Call LLM
    responses = await batch_call_deepseek(relationship_prompts)

    # Process relationships
    created_rels = 0
    with neo4j_conn.driver.session() as session:
        for (node1, node2), response in zip(node_pairs, responses):
            try:
                if isinstance(response, Exception):
                    logger.error(f"LLM 调用失败 for {node1['name']} -> {node2['name']}: {response}")
                    continue
                logger.debug(f"LLM 响应 for {node1['name']} -> {node2['name']}: {response}")
                content = None
                try:
                    if isinstance(response, str):
                        content = response.strip()
                    elif isinstance(response, dict):
                        content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
                        content = content.replace("\r\n", "\n").replace("\r", "\n").replace("\t", "").strip()
                        content = content.replace("```json\n", "").replace("\n```", "").replace("```", "").strip()
                        content = content.replace("\ufeff", "").encode('utf-8').decode('utf-8')
                        content = ''.join(c for c in content if c.isprintable() or c in '\n ')

                    # 更强健的 JSON 提取逻辑
                    if not content:
                        logger.error(f"LLM 响应为空 for {node1['name']} -> {node2['name']}")
                        continue

                    # 尝试找到 JSON 数组的开始和结束
                    start_idx = content.find('[')
                    end_idx = content.rfind(']')

                    if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
                        logger.error(f"LLM 响应中未找到有效的 JSON 数组 for {node1['name']} -> {node2['name']}: {content}")
                        continue

                    json_content = content[start_idx:end_idx+1]
                    relationships = json.loads(json_content)

                    if not isinstance(relationships, list):
                        logger.error(f"解析后 LLM 响应不是列表 for {node1['name']} -> {node2['name']}: {relationships}")
                        continue
                    logger.debug(f"解析成功 for {node1['name']} -> {node2['name']}: {relationships}")
                except json.JSONDecodeError as e:
                    logger.error(f"解析 LLM 响应失败 for {node1['name']} -> {node2['name']}: content={content}, 错误: {e}")
                    continue

                if not relationships:
                    logger.debug(f"空关系列表 for {node1['name']} -> {node2['name']}")
                    continue

                source_exists = await updater.crud.node_exists(session, "Attraction", node1["name"])
                target_exists = await updater.crud.node_exists(session, "Attraction", node2["name"])
                if not (source_exists and target_exists):
                    logger.error(f"节点不存在 for {node1['name']} -> {node2['name']}: source_exists={source_exists}, target_exists={target_exists}")
                    continue

                existing_rels = updater.crud.get_relationships(session, "Attraction", node1["name"])
                for rel in relationships:
                    try:
                        if not isinstance(rel, dict) or not all(k in rel for k in ["type", "reason", "confidence"]):
                            logger.error(f"无效的关系格式 for {node1['name']} -> {node2['name']}: {rel}")
                            continue
                        confidence = float(rel["confidence"])
                        rel_data = {
                            "source_name": node1["name"],
                            "target_name": node2["name"],
                            "type": rel["type"],
                            "properties": {"reason": rel["reason"], "confidence": confidence}
                        }
                        if await conflict_resolver.check_relationship_conflict(rel_data, existing_rels):
                            conflict_resolver.log_conflict({
                                "entity_name": f"{node1['name']}_{node2['name']}_{rel['type']}",
                                "conflict_data": rel_data,
                                "existing_data": existing_rels,
                                "timestamp": await convert_to_beijing_time()
                            })
                            logger.debug(f"关系冲突: {node1['name']} -[{rel['type']}]-> {node2['name']}")
                            continue
                        # 使用事务来创建关系
                        def create_rel_tx(tx):
                            updater.crud.create_relationship(
                                tx=tx,
                                source_label="Attraction",
                                source_name=node1["name"],
                                target_label="Attraction",
                                target_name=node2["name"],
                                rel_type=rel["type"],
                                properties={"reason": rel["reason"], "confidence": confidence}
                            )

                        session.execute_write(create_rel_tx)
                        created_rels += 1
                        logger.info(f"创建 {rel['type']} 关系: {node1['name']} -> {node2['name']}")
                    except Exception as e:
                        logger.error(f"创建关系失败 for {node1['name']} -> {node2['name']}, 关系: {rel}: {e}")
                        continue
            except Exception as e:
                logger.error(f"处理节点对失败: {node1['name']} -> {node2['name']}: {e}")
                continue

    logger.info(f"成功创建 {created_rels} 个关系")
    updater.close()
    return {
        "status": "success",
        "processed": len([r for r in results if r["status"] == "success"]),
        "failed": len([r for r in results if r["status"] == "failed"]),
        "relationships_created": created_rels,
        "details": results
    }
    
async def close_resources():
    """
    Close any resources if needed.
    """
    logger.info("关闭资源")
    
CITY_MAP = {
        "西藏拉萨": "拉萨市",
        "拉萨": "拉萨市",
        "林芝": "林芝市",
        "日喀则": "日喀则市",
        "昌都": "昌都市",
        "那曲": "那曲市",
        "阿里": "阿里地区",
        "山南": "山南市",
        "八廓街": "拉萨市",
        "西藏市": "拉萨市",
        "当雄县": "拉萨市",  # Normalize to Lhasa for tourist attractions
        "墨竹工卡县": "拉萨市",
        "林周县": "拉萨市",
        "尼木县": "拉萨市",
        "曲水县": "拉萨市"
}
