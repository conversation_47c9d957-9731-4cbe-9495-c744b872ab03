#!/usr/bin/env python3
"""
生成100个高质量的冲突解决测试用例

每个测试用例都是精心设计的，有明确的冲突类型、难度等级和预期结果
"""

import json
import random
from datetime import datetime, timedelta

def generate_100_test_cases():
    """生成100个高质量测试用例"""
    
    test_cases = []
    
    # 西藏相关实体和属性
    entities = [
        "布达拉宫", "大昭寺", "色拉寺", "哲蚌寺", "甘丹寺", "扎什伦布寺", "萨迦寺", "白居寺",
        "纳木错", "羊卓雍错", "巴松措", "然乌湖", "玛旁雍错",
        "珠穆朗玛峰", "南迦巴瓦峰", "冈仁波齐", "卓奥友峰",
        "西藏博物馆", "罗布林卡", "八廓街", "宗角禄康公园", "药王山",
        "林芝", "日喀则", "山南", "那曲", "阿里", "昌都"
    ]
    
    attributes = [
        "门票价格", "开放时间", "游览建议", "交通方式", "历史介绍", 
        "建筑特色", "宗教活动", "参观须知", "最佳游览时间", "海拔高度",
        "地理位置", "文化意义", "旅游路线", "住宿推荐", "美食特色"
    ]
    
    source_types = {
        "government": {"weight_range": (0.90, 0.98), "reliability": "highest"},
        "academic": {"weight_range": (0.85, 0.95), "reliability": "very_high"},
        "tourism_bureau": {"weight_range": (0.80, 0.90), "reliability": "high"},
        "official_site": {"weight_range": (0.75, 0.88), "reliability": "high"},
        "travel_guide": {"weight_range": (0.65, 0.80), "reliability": "medium_high"},
        "travel_site": {"weight_range": (0.55, 0.75), "reliability": "medium"},
        "news_media": {"weight_range": (0.60, 0.80), "reliability": "medium"},
        "forum": {"weight_range": (0.35, 0.55), "reliability": "medium_low"},
        "personal_blog": {"weight_range": (0.20, 0.40), "reliability": "low"},
        "social_media": {"weight_range": (0.15, 0.35), "reliability": "lowest"}
    }
    
    # 生成不同类型的测试用例
    
    # 1. 权重主导型 (30个)
    for i in range(30):
        entity = random.choice(entities)
        attribute = random.choice(attributes)
        
        # 选择高权重和低权重的源
        high_source = random.choice(["government", "academic", "tourism_bureau"])
        low_source = random.choice(["forum", "personal_blog", "social_media"])
        
        high_weight = random.uniform(*source_types[high_source]["weight_range"])
        low_weight = random.uniform(*source_types[low_source]["weight_range"])
        
        # 时间设置：高权重源较旧，低权重源较新
        old_time = datetime(2021, random.randint(1, 12), random.randint(1, 28))
        new_time = datetime(2023, random.randint(6, 12), random.randint(1, 28))
        
        test_case = {
            "id": i + 1,
            "name": f"权重主导_{entity}_{attribute}",
            "conflict_type": "weight_dominant",
            "difficulty": "easy" if high_weight - low_weight > 0.5 else "medium",
            "conflicts": [
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：权威详细信息，包含具体数据和官方说明",
                    "source": {
                        "type": high_source,
                        "url": f"http://{high_source}-{i}.com/{entity.lower()}",
                        "weight": round(high_weight, 2)
                    },
                    "timestamp": old_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                },
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：个人经验分享，可能不够准确",
                    "source": {
                        "type": low_source,
                        "url": f"http://{low_source}-{i}.com/{entity.lower()}",
                        "weight": round(low_weight, 2)
                    },
                    "timestamp": new_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
            ],
            "expected_winner_id": f"http://{high_source}-{i}.com/{entity.lower()}"
        }
        test_cases.append(test_case)
    
    # 2. 时间主导型 (25个)
    for i in range(25):
        entity = random.choice(entities)
        attribute = random.choice(attributes)
        
        # 选择权重相近的源
        source1 = random.choice(["travel_guide", "travel_site", "news_media"])
        source2 = random.choice(["travel_guide", "travel_site", "news_media"])
        
        weight1 = random.uniform(*source_types[source1]["weight_range"])
        weight2 = weight1 + random.uniform(-0.1, 0.1)  # 权重相近
        weight2 = max(0.1, min(0.9, weight2))  # 确保在合理范围内
        
        # 时间设置：明显的新旧差异
        old_time = datetime(2020, random.randint(1, 12), random.randint(1, 28))
        new_time = datetime(2023, random.randint(8, 12), random.randint(1, 28))
        
        test_case = {
            "id": i + 31,
            "name": f"时间主导_{entity}_{attribute}",
            "conflict_type": "time_dominant",
            "difficulty": "medium",
            "conflicts": [
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：较旧的信息，可能已经过时",
                    "source": {
                        "type": source1,
                        "url": f"http://{source1}-old-{i}.com/{entity.lower()}",
                        "weight": round(weight1, 2)
                    },
                    "timestamp": old_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                },
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：最新更新的信息，反映当前情况",
                    "source": {
                        "type": source2,
                        "url": f"http://{source2}-new-{i}.com/{entity.lower()}",
                        "weight": round(weight2, 2)
                    },
                    "timestamp": new_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
            ],
            "expected_winner_id": f"http://{source2}-new-{i}.com/{entity.lower()}"
        }
        test_cases.append(test_case)
    
    # 3. 复杂权衡型 (25个)
    for i in range(25):
        entity = random.choice(entities)
        attribute = random.choice(attributes)
        
        # 中等权重差异，中等时间差异
        high_source = random.choice(["tourism_bureau", "official_site"])
        low_source = random.choice(["travel_guide", "travel_site"])
        
        high_weight = random.uniform(*source_types[high_source]["weight_range"])
        low_weight = random.uniform(*source_types[low_source]["weight_range"])
        
        # 时间设置：权重高的较旧，权重低的较新
        old_time = datetime(2021, random.randint(6, 12), random.randint(1, 28))
        new_time = datetime(2023, random.randint(3, 11), random.randint(1, 28))
        
        # 根据权重差异和时间差异决定预期获胜者
        weight_diff = high_weight - low_weight
        time_diff_days = (new_time - old_time).days
        
        # 如果权重差异小且时间差异大，新数据获胜
        if weight_diff < 0.2 and time_diff_days > 500:
            expected_winner = f"http://{low_source}-new-{i}.com/{entity.lower()}"
        else:
            expected_winner = f"http://{high_source}-old-{i}.com/{entity.lower()}"
        
        test_case = {
            "id": i + 56,
            "name": f"复杂权衡_{entity}_{attribute}",
            "conflict_type": "complex_tradeoff",
            "difficulty": "hard",
            "conflicts": [
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：权威但可能过时的信息",
                    "source": {
                        "type": high_source,
                        "url": f"http://{high_source}-old-{i}.com/{entity.lower()}",
                        "weight": round(high_weight, 2)
                    },
                    "timestamp": old_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                },
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": f"{attribute}：较新但权威性稍低的信息",
                    "source": {
                        "type": low_source,
                        "url": f"http://{low_source}-new-{i}.com/{entity.lower()}",
                        "weight": round(low_weight, 2)
                    },
                    "timestamp": new_time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
            ],
            "expected_winner_id": expected_winner
        }
        test_cases.append(test_case)
    
    # 4. 边界情况 (20个)
    for i in range(20):
        entity = random.choice(entities)
        attribute = random.choice(attributes)
        
        # 权重和时间都非常接近的情况
        base_weight = random.uniform(0.6, 0.8)
        weight1 = base_weight
        weight2 = base_weight + random.uniform(-0.05, 0.05)
        
        base_time = datetime(2023, 6, 15)
        time1 = base_time
        time2 = base_time + timedelta(hours=random.randint(-24, 24))
        
        # 在这种情况下，内容质量成为决定因素
        detailed_content = f"{attribute}：详细完整的信息，包含具体数据、时间安排、注意事项等全面内容"
        simple_content = f"{attribute}：简单信息"
        
        test_case = {
            "id": i + 81,
            "name": f"边界情况_{entity}_{attribute}",
            "conflict_type": "edge_case",
            "difficulty": "hard",
            "conflicts": [
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": simple_content,
                    "source": {
                        "type": "travel_site",
                        "url": f"http://simple-{i}.com/{entity.lower()}",
                        "weight": round(weight1, 2)
                    },
                    "timestamp": time1.strftime("%Y-%m-%dT%H:%M:%SZ")
                },
                {
                    "entity_name": entity,
                    "attribute": attribute,
                    "value": detailed_content,
                    "source": {
                        "type": "travel_guide",
                        "url": f"http://detailed-{i}.com/{entity.lower()}",
                        "weight": round(weight2, 2)
                    },
                    "timestamp": time2.strftime("%Y-%m-%dT%H:%M:%SZ")
                }
            ],
            "expected_winner_id": f"http://detailed-{i}.com/{entity.lower()}"  # 内容更详细的获胜
        }
        test_cases.append(test_case)
    
    return test_cases

def save_test_dataset():
    """保存测试数据集到文件"""
    
    print("🚀 生成100个高质量测试用例...")
    
    test_cases = generate_100_test_cases()
    
    # 保存为JSON文件
    with open('test_dataset_100_complete.json', 'w', encoding='utf-8') as f:
        json.dump(test_cases, f, ensure_ascii=False, indent=2)
    
    # 统计信息
    type_counts = {}
    difficulty_counts = {}
    
    for case in test_cases:
        conflict_type = case['conflict_type']
        difficulty = case['difficulty']
        
        type_counts[conflict_type] = type_counts.get(conflict_type, 0) + 1
        difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
    
    print(f"✅ 成功生成 {len(test_cases)} 个测试用例")
    print(f"📊 冲突类型分布: {type_counts}")
    print(f"📊 难度分布: {difficulty_counts}")
    print(f"💾 已保存到: test_dataset_100_complete.json")
    
    return test_cases

if __name__ == "__main__":
    save_test_dataset()
