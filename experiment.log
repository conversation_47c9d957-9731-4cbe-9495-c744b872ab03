2025-07-22 10:03:24,473 - __main__ - ERROR - 实验执行失败: 'username'
Traceback (most recent call last):
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\run_experiment.py", line 66, in main
    neo4j_config["username"],
    ~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'username'
2025-07-22 10:04:26,048 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-22 10:04:26,048 - __main__ - ERROR - 实验执行失败: 'Neo4jConnection' object has no attribute 'verify_connection'
Traceback (most recent call last):
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\run_experiment.py", line 70, in main
    if not neo4j_conn.verify_connection():
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Neo4jConnection' object has no attribute 'verify_connection'
2025-07-22 10:04:26,050 - neo4j_connection - INFO - Neo4j 连接已关闭
2025-07-22 10:06:42,984 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-22 10:06:43,041 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-22 10:06:43,043 - evolution_quality_experiment - INFO - 加载黄金标准数据集: 12 个实体, 18 个关系
2025-07-22 10:06:43,044 - evolution_quality_experiment - INFO - 开始知识图谱演化质量评估实验
2025-07-22 10:06:43,044 - evolution_quality_experiment - INFO - 生成测试数据: 15 条记录
2025-07-22 10:06:43,044 - evolution_quality_experiment - INFO - 开始多策略代理实验...
2025-07-22 10:06:43,981 - evolution_quality_experiment - INFO - 数据库已清空
2025-07-22 10:06:43,981 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-22 10:06:43,982 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-22 10:06:43,982 - text_processor - INFO - text_processor.py version: 2025-07-22-v7, Enhanced Conflict Resolution
2025-07-22 10:06:43,982 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-22 10:06:43,983 - enhanced_conflict_resolution - INFO - 增强冲突解决器初始化完成
2025-07-22 10:06:43,983 - text_processor - INFO - 使用增强冲突解决器
2025-07-22 10:06:44,626 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-22 10:06:44,654 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-22 10:06:44,683 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-22 10:06:44,711 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-22 10:06:44,739 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-22 10:06:44,766 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-22 10:06:44,799 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-22 10:06:44,827 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-22 10:06:44,854 - text_processor - INFO - 成功处理实体: 药王山
2025-07-22 10:06:44,883 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-22 10:06:44,918 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-22 10:06:44,945 - text_processor - INFO - 成功处理实体: 羊卓雍措
2025-07-22 10:06:44,976 - text_processor - INFO - 成功处理实体: 扎什伦布寺
2025-07-22 10:06:45,006 - text_processor - INFO - 成功处理实体: 珠穆朗玛峰
2025-07-22 10:06:45,035 - text_processor - INFO - 成功处理实体: 巴松措
2025-07-22 10:06:45,035 - text_processor - INFO - 过滤后景点数量: 15
2025-07-22 10:06:45,035 - text_processor - WARNING - 跳过重复或无效名称对: 大昭寺 -> 大昭寺
2025-07-22 10:06:45,036 - text_processor - WARNING - 跳过重复或无效名称: 大昭寺
2025-07-22 10:06:45,036 - text_processor - WARNING - 跳过重复或无效名称对: 色拉寺 -> 色拉寺
2025-07-22 10:06:45,036 - text_processor - WARNING - 跳过重复或无效名称: 色拉寺
2025-07-22 10:06:45,036 - text_processor - WARNING - 跳过重复或无效名称对: 哲蚌寺 -> 哲蚌寺
2025-07-22 10:06:45,036 - text_processor - WARNING - 跳过重复或无效名称: 哲蚌寺
2025-07-22 10:06:45,037 - text_processor - INFO - 生成 77 个节点对进行关系推断
2025-07-22 10:11:04,576 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 大昭寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,584 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 大昭寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,586 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,589 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,592 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,596 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:11:04,599 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:11:04,603 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,605 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,609 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,615 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,619 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,623 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,627 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,629 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,633 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,636 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,640 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:11:04,642 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:11:04,646 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,650 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,653 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,658 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,661 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,664 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,668 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,672 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,676 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,679 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:11:04,681 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:11:04,684 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,687 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,689 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,693 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,697 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,698 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,703 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,706 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:11:04,709 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:11:04,712 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,716 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,719 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,724 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,726 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,729 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,732 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,735 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:11:04,738 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,741 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,744 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,747 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,750 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,754 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,757 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,760 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:11:04,763 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,767 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,770 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,773 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,777 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,781 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,784 - text_processor - ERROR - 处理节点对失败: 药王山 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,787 - text_processor - ERROR - 处理节点对失败: 药王山 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,791 - text_processor - ERROR - 处理节点对失败: 药王山 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,794 - text_processor - ERROR - 处理节点对失败: 药王山 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,797 - text_processor - ERROR - 处理节点对失败: 药王山 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,800 - text_processor - ERROR - 处理节点对失败: 药王山 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,803 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,806 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,808 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,812 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,814 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:11:04,818 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,822 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,827 - text_processor - ERROR - 处理节点对失败: 扎什伦布寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:11:04,829 - text_processor - ERROR - 处理节点对失败: 扎什伦布寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,832 - text_processor - ERROR - 处理节点对失败: 珠穆朗玛峰 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:11:04,832 - text_processor - INFO - 成功创建 0 个关系
2025-07-22 10:11:04,833 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-22 10:11:04,914 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: category)} {position: line: 3, column: 46, offset: 87} for query: '\n                    MATCH (a:Attraction)\n                    RETURN a.name as name, a.category as category,\n                           a.location as location, a.visitor_percentage as visitor_percentage\n                '
2025-07-22 10:11:04,915 - evolution_quality_experiment - INFO - 多策略代理 指标计算完成: 准确率=0.000, 覆盖率=1.000, F1=0.000
2025-07-22 10:11:04,916 - evolution_quality_experiment - INFO - 开始单策略基准实验...
2025-07-22 10:11:04,939 - evolution_quality_experiment - INFO - 数据库已清空
2025-07-22 10:11:04,939 - evolution_quality_experiment - ERROR - 单策略基准实验失败: 'BoltDriver' object has no attribute 'driver'
2025-07-22 10:11:04,940 - evolution_quality_experiment - ERROR - 实验执行失败: 'BoltDriver' object has no attribute 'driver'
2025-07-22 10:11:04,940 - __main__ - ERROR - 实验执行失败: 'BoltDriver' object has no attribute 'driver'
Traceback (most recent call last):
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\run_experiment.py", line 84, in main
    results = await experiment.run_complete_experiment()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 413, in run_complete_experiment
    single_strategy_result = await self.run_single_strategy_experiment(test_data)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 268, in run_single_strategy_experiment
    result = await self.simple_single_strategy_processing(test_data)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 293, in simple_single_strategy_processing
    single_processor = SingleStrategyProcessor(self.neo4j_conn)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\single_strategy_baseline.py", line 42, in __init__
    self.crud = Neo4jCRUD(neo4j_conn.driver)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\neo4j_crud.py", line 15, in __init__
    self.driver = connection.driver
                  ^^^^^^^^^^^^^^^^^
AttributeError: 'BoltDriver' object has no attribute 'driver'
2025-07-22 10:11:04,948 - neo4j_connection - INFO - Neo4j 连接已关闭
2025-07-22 10:16:34,473 - neo4j_connection - INFO - 初始化 Neo4j 连接: bolt://localhost:7687, 用户: neo4j
2025-07-22 10:16:34,518 - neo4j_connection - INFO - Neo4j 连接验证成功
2025-07-22 10:16:34,520 - evolution_quality_experiment - INFO - 加载黄金标准数据集: 12 个实体, 18 个关系
2025-07-22 10:16:34,521 - evolution_quality_experiment - INFO - 开始知识图谱演化质量评估实验
2025-07-22 10:16:34,522 - evolution_quality_experiment - INFO - 生成测试数据: 15 条记录
2025-07-22 10:16:34,522 - evolution_quality_experiment - INFO - 开始多策略代理实验...
2025-07-22 10:16:34,525 - evolution_quality_experiment - INFO - 数据库已清空
2025-07-22 10:16:34,526 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-22 10:16:34,526 - conflict_resolution - INFO - ConflictResolver initialized
2025-07-22 10:16:34,526 - text_processor - INFO - text_processor.py version: 2025-07-22-v7, Enhanced Conflict Resolution
2025-07-22 10:16:34,527 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-22 10:16:34,527 - enhanced_conflict_resolution - INFO - 增强冲突解决器初始化完成
2025-07-22 10:16:34,527 - text_processor - INFO - 使用增强冲突解决器
2025-07-22 10:16:34,689 - text_processor - INFO - 成功处理实体: 布达拉宫
2025-07-22 10:16:34,705 - text_processor - INFO - 成功处理实体: 大昭寺
2025-07-22 10:16:34,721 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-22 10:16:34,736 - text_processor - INFO - 成功处理实体: 纳木措
2025-07-22 10:16:34,751 - text_processor - INFO - 成功处理实体: 色拉寺
2025-07-22 10:16:34,774 - text_processor - INFO - 成功处理实体: 八廓街
2025-07-22 10:16:34,792 - text_processor - INFO - 成功处理实体: 罗布林卡
2025-07-22 10:16:34,808 - text_processor - INFO - 成功处理实体: 药王山
2025-07-22 10:16:34,825 - text_processor - INFO - 成功处理实体: 哲蚌寺
2025-07-22 10:16:34,842 - text_processor - INFO - 成功处理实体: 羊卓雍措
2025-07-22 10:16:34,860 - text_processor - INFO - 成功处理实体: 扎什伦布寺
2025-07-22 10:16:34,876 - text_processor - INFO - 成功处理实体: 扎什伦布寺
2025-07-22 10:16:34,891 - text_processor - INFO - 成功处理实体: 珠穆朗玛峰
2025-07-22 10:16:34,907 - text_processor - INFO - 成功处理实体: 巴松措
2025-07-22 10:16:34,924 - text_processor - INFO - 成功处理实体: 巴松措
2025-07-22 10:16:34,925 - text_processor - INFO - 过滤后景点数量: 15
2025-07-22 10:16:34,925 - text_processor - WARNING - 跳过重复或无效名称对: 纳木措 -> 纳木措
2025-07-22 10:16:34,926 - text_processor - WARNING - 跳过重复或无效名称: 纳木措
2025-07-22 10:16:34,926 - text_processor - WARNING - 跳过重复或无效名称对: 扎什伦布寺 -> 扎什伦布寺
2025-07-22 10:16:34,926 - text_processor - WARNING - 跳过重复或无效名称: 扎什伦布寺
2025-07-22 10:16:34,926 - text_processor - WARNING - 跳过重复或无效名称对: 巴松措 -> 巴松措
2025-07-22 10:16:34,927 - text_processor - WARNING - 跳过重复或无效名称: 巴松措
2025-07-22 10:16:34,927 - text_processor - INFO - 生成 88 个节点对进行关系推断
2025-07-22 10:20:12,285 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 大昭寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,288 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,290 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,293 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,297 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:20:12,301 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:20:12,305 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,308 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,313 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,316 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,320 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,323 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,327 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,330 - text_processor - ERROR - 处理节点对失败: 布达拉宫 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,334 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,338 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 纳木措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,342 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,345 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:20:12,348 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:20:12,352 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,354 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,358 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,361 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,364 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,366 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,370 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,373 - text_processor - ERROR - 处理节点对失败: 大昭寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,376 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 色拉寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,379 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:20:12,383 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:20:12,386 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,388 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,392 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,395 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,397 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,400 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,404 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,407 - text_processor - ERROR - 处理节点对失败: 纳木措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,410 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 八廓街: object bool can't be used in 'await' expression
2025-07-22 10:20:12,414 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:20:12,417 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,420 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,424 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,427 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,430 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,432 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,437 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,440 - text_processor - ERROR - 处理节点对失败: 色拉寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,443 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 罗布林卡: object bool can't be used in 'await' expression
2025-07-22 10:20:12,446 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,450 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,453 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,455 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,459 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,462 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,464 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,467 - text_processor - ERROR - 处理节点对失败: 八廓街 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,471 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 药王山: object bool can't be used in 'await' expression
2025-07-22 10:20:12,473 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,475 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,479 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,482 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,486 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,490 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,493 - text_processor - ERROR - 处理节点对失败: 罗布林卡 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,497 - text_processor - ERROR - 处理节点对失败: 药王山 -> 哲蚌寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,500 - text_processor - ERROR - 处理节点对失败: 药王山 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,503 - text_processor - ERROR - 处理节点对失败: 药王山 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,507 - text_processor - ERROR - 处理节点对失败: 药王山 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,509 - text_processor - ERROR - 处理节点对失败: 药王山 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,514 - text_processor - ERROR - 处理节点对失败: 药王山 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,517 - text_processor - ERROR - 处理节点对失败: 药王山 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,529 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 羊卓雍措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,532 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,535 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,539 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,541 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,544 - text_processor - ERROR - 处理节点对失败: 哲蚌寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,547 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,550 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 扎什伦布寺: object bool can't be used in 'await' expression
2025-07-22 10:20:12,553 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,555 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,559 - text_processor - ERROR - 处理节点对失败: 羊卓雍措 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,562 - text_processor - ERROR - 处理节点对失败: 扎什伦布寺 -> 珠穆朗玛峰: object bool can't be used in 'await' expression
2025-07-22 10:20:12,566 - text_processor - ERROR - 处理节点对失败: 扎什伦布寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,569 - text_processor - ERROR - 处理节点对失败: 扎什伦布寺 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,573 - text_processor - ERROR - 处理节点对失败: 珠穆朗玛峰 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,575 - text_processor - ERROR - 处理节点对失败: 珠穆朗玛峰 -> 巴松措: object bool can't be used in 'await' expression
2025-07-22 10:20:12,576 - text_processor - INFO - 成功创建 0 个关系
2025-07-22 10:20:12,576 - neo4j_crud - INFO - Neo4jCRUD connection closed
2025-07-22 10:20:12,600 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: category)} {position: line: 3, column: 46, offset: 87} for query: '\n                    MATCH (a:Attraction)\n                    RETURN a.name as name, a.category as category,\n                           a.location as location, a.visitor_percentage as visitor_percentage\n                '
2025-07-22 10:20:12,601 - evolution_quality_experiment - INFO - 多策略代理 指标计算完成: 准确率=0.000, 覆盖率=1.000, F1=0.000
2025-07-22 10:20:12,601 - evolution_quality_experiment - INFO - 开始单策略基准实验...
2025-07-22 10:20:12,619 - evolution_quality_experiment - INFO - 数据库已清空
2025-07-22 10:20:12,619 - neo4j_crud - INFO - Neo4jCRUD initialized with provided connection
2025-07-22 10:20:12,619 - single_strategy_baseline - INFO - 单策略处理器初始化完成
2025-07-22 10:20:12,619 - single_strategy_baseline - INFO - 数据验证完成，有效数据: 15 条
2025-07-22 10:20:13,156 - single_strategy_baseline - WARNING - 检测到重复实体: 纳木措 (出现 2 次)
2025-07-22 10:20:13,156 - single_strategy_baseline - WARNING - 检测到重复实体: 扎什伦布寺 (出现 2 次)
2025-07-22 10:20:13,157 - single_strategy_baseline - WARNING - 检测到重复实体: 巴松措 (出现 2 次)
2025-07-22 10:20:13,158 - single_strategy_baseline - INFO - 冲突记录已保存，共检测到 3 个冲突
2025-07-22 10:20:13,158 - single_strategy_baseline - INFO - 单策略处理完成: 处理 15 个实体, 创建 15 个关系, 检测到 3 个冲突
2025-07-22 10:20:13,177 - evolution_quality_experiment - INFO - 单策略基准 指标计算完成: 准确率=1.000, 覆盖率=1.000, F1=1.000
2025-07-22 10:20:13,179 - evolution_quality_experiment - INFO - 实验结果已保存到 experiment_results.json
2025-07-22 10:20:13,637 - evolution_quality_experiment - ERROR - 实验执行失败: 'Axes' object has no attribute 'set_theta_offset'
2025-07-22 10:20:13,638 - __main__ - ERROR - 实验执行失败: 'Axes' object has no attribute 'set_theta_offset'
Traceback (most recent call last):
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\run_experiment.py", line 84, in main
    results = await experiment.run_complete_experiment()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 422, in run_complete_experiment
    await self.generate_visualization()
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 471, in generate_visualization
    self.create_radar_chart(ax1, strategies, metrics_data)
  File "D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\动态知识图谱的自适应演化\evolution_quality_experiment.py", line 498, in create_radar_chart
    ax.set_theta_offset(np.pi / 2)
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'Axes' object has no attribute 'set_theta_offset'
2025-07-22 10:20:13,642 - neo4j_connection - INFO - Neo4j 连接已关闭
