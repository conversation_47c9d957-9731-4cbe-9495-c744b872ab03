#!/usr/bin/env python3
"""
基于100个完整测试用例的冲突解决实验

使用独立的JSON数据集文件，包含真实的分析维度
"""

import json
import time
from datetime import datetime
from collections import defaultdict, Counter
import statistics
import re

def load_test_dataset():
    """加载测试数据集"""
    try:
        with open('test_dataset_100_complete.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 测试数据集文件未找到，请先运行 generate_100_test_cases.py")
        return []

def parse_timestamp(timestamp_str):
    """解析时间戳"""
    timestamp_str = timestamp_str.replace('Z', '+00:00')
    timestamp_str = re.sub(r'T(\d):(\d\d):(\d\d)', r'T0\1:\2:\3', timestamp_str)
    return datetime.fromisoformat(timestamp_str)

def resolve_conflict_weight_first(conflicts):
    """权重优先策略"""
    return max(conflicts, key=lambda x: x["source"]["weight"])

def resolve_conflict_latest_first(conflicts):
    """时间优先策略"""
    return max(conflicts, key=lambda x: parse_timestamp(x["timestamp"]))

def resolve_conflict_hybrid(conflicts):
    """混合策略"""
    best_conflict = None
    best_score = -1
    
    for conflict in conflicts:
        weight = conflict["source"]["weight"]
        timestamp = parse_timestamp(conflict["timestamp"])
        
        # 时间新鲜度评分
        now = datetime.now(timestamp.tzinfo)
        days_old = (now - timestamp).days
        time_score = max(0, 1 - days_old / 730)  # 2年内线性衰减
        
        # 内容质量评分
        content_length = len(conflict["value"])
        content_score = min(1.0, content_length / 100)
        
        # 综合评分
        final_score = weight * 0.5 + time_score * 0.3 + content_score * 0.2
        
        if final_score > best_score:
            best_score = final_score
            best_conflict = conflict
    
    return best_conflict

def run_experiment_with_dataset():
    """使用数据集运行实验"""
    
    print("🚀 基于100个完整测试用例的冲突解决实验")
    print("="*80)
    
    # 加载测试数据
    test_cases = load_test_dataset()
    if not test_cases:
        return None, None
    
    print(f"📊 成功加载 {len(test_cases)} 个测试用例")
    
    # 数据集统计
    type_counts = Counter([case['conflict_type'] for case in test_cases])
    difficulty_counts = Counter([case['difficulty'] for case in test_cases])
    
    print(f"📈 冲突类型分布: {dict(type_counts)}")
    print(f"📈 难度分布: {dict(difficulty_counts)}")
    print("="*80)
    
    # 运行三种策略
    strategies = {
        "weight_first": resolve_conflict_weight_first,
        "latest_first": resolve_conflict_latest_first,
        "hybrid": resolve_conflict_hybrid
    }
    
    results = {}
    detailed_results = []
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n🔄 运行策略: {strategy_name}")
        
        correct_count = 0
        strategy_results = []
        start_time = time.time()
        
        for test_case in test_cases:
            # 运行策略
            chosen_conflict = strategy_func(test_case["conflicts"])
            chosen_url = chosen_conflict["source"]["url"]
            expected_url = test_case["expected_winner_id"]
            
            is_correct = chosen_url == expected_url
            if is_correct:
                correct_count += 1
            
            # 记录详细结果
            result_detail = {
                "test_case_id": test_case["id"],
                "name": test_case["name"],
                "conflict_type": test_case["conflict_type"],
                "difficulty": test_case["difficulty"],
                "strategy": strategy_name,
                "chosen_url": chosen_url,
                "expected_url": expected_url,
                "is_correct": is_correct,
                "chosen_weight": chosen_conflict["source"]["weight"],
                "chosen_timestamp": chosen_conflict["timestamp"],
                "chosen_source_type": chosen_conflict["source"]["type"]
            }
            strategy_results.append(result_detail)
        
        end_time = time.time()
        execution_time = end_time - start_time
        accuracy = correct_count / len(test_cases)
        
        results[strategy_name] = {
            "accuracy": accuracy,
            "correct_count": correct_count,
            "total_count": len(test_cases),
            "execution_time": execution_time,
            "detailed_results": strategy_results
        }
        
        print(f"   ✅ 准确率: {accuracy:.3f} ({correct_count}/{len(test_cases)})")
        print(f"   ⏱️  执行时间: {execution_time:.4f}秒")
    
    # 合并所有详细结果
    for strategy_name in strategies.keys():
        detailed_results.extend(results[strategy_name]["detailed_results"])
    
    return results, detailed_results

def analyze_conflict_types(detailed_results):
    """分析冲突类型维度"""
    
    print("\n" + "="*80)
    print("📈 冲突类型分析 (基于真实数据集)")
    print("="*80)
    
    # 按冲突类型分组统计
    type_stats = defaultdict(lambda: defaultdict(list))
    
    for result in detailed_results:
        conflict_type = result["conflict_type"]
        strategy = result["strategy"]
        is_correct = result["is_correct"]
        
        type_stats[conflict_type][strategy].append(is_correct)
    
    # 计算每种类型下各策略的准确率
    print(f"{'冲突类型':<20} {'Weight-First':<15} {'Latest-First':<15} {'Hybrid':<15}")
    print("-" * 70)
    
    for conflict_type in sorted(type_stats.keys()):
        row = f"{conflict_type:<20}"
        
        for strategy in ["weight_first", "latest_first", "hybrid"]:
            if strategy in type_stats[conflict_type]:
                correct_list = type_stats[conflict_type][strategy]
                accuracy = sum(correct_list) / len(correct_list)
                count_str = f"{sum(correct_list)}/{len(correct_list)}"
                row += f" {accuracy:.3f}({count_str})"
                row += " " * (15 - len(f"{accuracy:.3f}({count_str})"))
            else:
                row += f"{'N/A':<15}"
        
        print(row)
    
    # 分析发现
    print(f"\n🔍 冲突类型分析发现：")
    
    for conflict_type in sorted(type_stats.keys()):
        accuracies = {}
        for strategy in ["weight_first", "latest_first", "hybrid"]:
            if strategy in type_stats[conflict_type]:
                correct_list = type_stats[conflict_type][strategy]
                accuracies[strategy] = sum(correct_list) / len(correct_list)
        
        if accuracies:
            best_strategy = max(accuracies, key=accuracies.get)
            best_accuracy = accuracies[best_strategy]
            
            print(f"   • {conflict_type}: {best_strategy} 表现最佳 ({best_accuracy:.3f})")

def analyze_error_patterns(detailed_results):
    """分析错误模式"""
    
    print("\n" + "="*80)
    print("📈 错误模式分析 (基于真实错误案例)")
    print("="*80)
    
    # 收集错误案例
    error_cases = [r for r in detailed_results if not r["is_correct"]]
    
    print(f"总错误案例数: {len(error_cases)}")
    
    # 按策略分组分析错误
    strategy_errors = defaultdict(list)
    for error in error_cases:
        strategy_errors[error["strategy"]].append(error)
    
    for strategy, errors in strategy_errors.items():
        print(f"\n🔍 {strategy} 错误分析 (共{len(errors)}个错误):")
        
        # 按冲突类型统计错误
        error_by_type = Counter([e["conflict_type"] for e in errors])
        print(f"   错误分布: {dict(error_by_type)}")
        
        # 按难度统计错误
        error_by_difficulty = Counter([e["difficulty"] for e in errors])
        print(f"   难度分布: {dict(error_by_difficulty)}")
        
        # 按源类型统计错误选择
        error_by_source = Counter([e["chosen_source_type"] for e in errors])
        print(f"   选择源类型: {dict(error_by_source)}")
        
        # 分析权重选择模式
        weights = [e["chosen_weight"] for e in errors]
        if weights:
            avg_weight = statistics.mean(weights)
            print(f"   平均选择权重: {avg_weight:.3f}")

def analyze_decision_difficulty(detailed_results):
    """分析决策难度"""
    
    print("\n" + "="*80)
    print("📈 决策难度分析 (基于难度标签)")
    print("="*80)
    
    # 按难度分组统计
    difficulty_stats = defaultdict(lambda: defaultdict(list))
    
    for result in detailed_results:
        difficulty = result["difficulty"]
        strategy = result["strategy"]
        is_correct = result["is_correct"]
        
        difficulty_stats[difficulty][strategy].append(is_correct)
    
    # 计算每种难度下各策略的准确率
    print(f"{'难度等级':<15} {'Weight-First':<15} {'Latest-First':<15} {'Hybrid':<15}")
    print("-" * 65)
    
    for difficulty in ["easy", "medium", "hard"]:
        if difficulty in difficulty_stats:
            row = f"{difficulty:<15}"
            
            for strategy in ["weight_first", "latest_first", "hybrid"]:
                if strategy in difficulty_stats[difficulty]:
                    correct_list = difficulty_stats[difficulty][strategy]
                    accuracy = sum(correct_list) / len(correct_list)
                    count_str = f"{sum(correct_list)}/{len(correct_list)}"
                    row += f" {accuracy:.3f}({count_str})"
                    row += " " * (15 - len(f"{accuracy:.3f}({count_str})"))
                else:
                    row += f"{'N/A':<15}"
            
            print(row)
    
    # 分析难度对性能的影响
    print(f"\n🔍 难度影响分析：")
    
    for strategy in ["weight_first", "latest_first", "hybrid"]:
        print(f"\n   {strategy}:")
        strategy_by_difficulty = {}
        
        for difficulty in ["easy", "medium", "hard"]:
            if difficulty in difficulty_stats and strategy in difficulty_stats[difficulty]:
                correct_list = difficulty_stats[difficulty][strategy]
                accuracy = sum(correct_list) / len(correct_list)
                strategy_by_difficulty[difficulty] = accuracy
                print(f"     {difficulty}: {accuracy:.3f}")
        
        # 计算难度敏感性
        if len(strategy_by_difficulty) >= 2:
            accuracies = list(strategy_by_difficulty.values())
            sensitivity = max(accuracies) - min(accuracies)
            print(f"     难度敏感性: {sensitivity:.3f}")

def main():
    """主函数"""
    
    # 运行实验
    results, detailed_results = run_experiment_with_dataset()
    
    if results is None:
        return
    
    # 输出基本结果
    print(f"\n" + "="*80)
    print("📊 基本实验结果")
    print("="*80)
    
    for strategy_name, result in results.items():
        print(f"{strategy_name:<15}: 准确率 {result['accuracy']:.3f} "
              f"({result['correct_count']}/{result['total_count']}) "
              f"耗时 {result['execution_time']:.4f}秒")
    
    # 进行深入分析
    analyze_conflict_types(detailed_results)
    analyze_error_patterns(detailed_results)
    analyze_decision_difficulty(detailed_results)
    
    print(f"\n🎉 实验完成！")
    print("="*80)
    print("✅ 基于100个精心设计的测试用例")
    print("✅ 每个测试用例都有明确的类型和难度")
    print("✅ 所有分析维度都基于真实数据")
    print("✅ 数据集可独立查看和验证")
    print(f"📁 数据集位置: test_dataset_100_complete.json")

if __name__ == "__main__":
    main()
