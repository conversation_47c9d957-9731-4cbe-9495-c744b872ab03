#!/usr/bin/env python3
"""
实验思路、流程和原理的思维导图

使用matplotlib和networkx创建实验设计的思维导图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def create_experiment_mindmap():
    """创建实验思维导图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 14)
    ax.axis('off')
    
    # 定义颜色方案
    colors = {
        'core': '#FF6B6B',      # 核心节点 - 红色
        'problem': '#FFA07A',    # 问题发现 - 橙色
        'design': '#45B7D1',     # 设计思路 - 蓝色
        'method': '#32CD32',     # 方法实现 - 绿色
        'result': '#FFD700',     # 结果验证 - 金色
        'insight': '#9370DB'     # 洞察发现 - 紫色
    }
    
    # 中心节点
    center_box = FancyBboxPatch((8.5, 6.5), 3, 1.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['core'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(center_box)
    ax.text(10, 7.25, '基于拉萨知识图谱的\n冲突解决策略实验', 
            ha='center', va='center', fontsize=14, fontweight='bold', color='white')
    
    # 第一层：主要分支
    branches = [
        {'pos': (4, 11), 'size': (2.5, 1), 'text': '问题发现\nProblem Discovery', 'color': colors['problem']},
        {'pos': (14, 11), 'size': (2.5, 1), 'text': '实验设计\nExperiment Design', 'color': colors['design']},
        {'pos': (2, 7), 'size': (2.5, 1), 'text': '数据基础\nData Foundation', 'color': colors['method']},
        {'pos': (16, 7), 'size': (2.5, 1), 'text': '策略实现\nStrategy Implementation', 'color': colors['method']},
        {'pos': (4, 3), 'size': (2.5, 1), 'text': '实验验证\nExperimental Validation', 'color': colors['result']},
        {'pos': (14, 3), 'size': (2.5, 1), 'text': '结果分析\nResult Analysis', 'color': colors['result']}
    ]
    
    # 绘制主要分支
    for branch in branches:
        box = FancyBboxPatch(branch['pos'], branch['size'][0], branch['size'][1],
                            boxstyle="round,pad=0.05", 
                            facecolor=branch['color'], 
                            edgecolor='black', linewidth=1.5)
        ax.add_patch(box)
        ax.text(branch['pos'][0] + branch['size'][0]/2, 
                branch['pos'][1] + branch['size'][1]/2, 
                branch['text'], 
                ha='center', va='center', fontsize=11, fontweight='bold', color='white')
        
        # 连接到中心节点
        center_x, center_y = 10, 7.25
        branch_x = branch['pos'][0] + branch['size'][0]/2
        branch_y = branch['pos'][1] + branch['size'][1]/2
        
        ax.plot([center_x, branch_x], [center_y, branch_y], 
                'k-', linewidth=2, alpha=0.7)
    
    # 第二层：详细内容
    details = [
        # 问题发现分支
        {'pos': (1, 12.5), 'size': (2, 0.6), 'text': '固定权重局限性', 'parent': (4, 11), 'color': colors['problem']},
        {'pos': (1, 11.7), 'size': (2, 0.6), 'text': '缺乏场景适应性', 'parent': (4, 11), 'color': colors['problem']},
        {'pos': (1, 10.9), 'size': (2, 0.6), 'text': '困难场景表现差', 'parent': (4, 11), 'color': colors['problem']},
        
        # 实验设计分支
        {'pos': (17, 12.5), 'size': (2, 0.6), 'text': '场景适应性假设', 'parent': (14, 11), 'color': colors['design']},
        {'pos': (17, 11.7), 'size': (2, 0.6), 'text': '动态权重假设', 'parent': (14, 11), 'color': colors['design']},
        {'pos': (17, 10.9), 'size': (2, 0.6), 'text': '极化配置假设', 'parent': (14, 11), 'color': colors['design']},
        
        # 数据基础分支
        {'pos': (0.2, 8.5), 'size': (1.8, 0.6), 'text': '260个真实景点', 'parent': (2, 7), 'color': colors['method']},
        {'pos': (0.2, 7.7), 'size': (1.8, 0.6), 'text': '100个测试案例', 'parent': (2, 7), 'color': colors['method']},
        {'pos': (0.2, 6.9), 'size': (1.8, 0.6), 'text': '4种冲突类型', 'parent': (2, 7), 'color': colors['method']},
        {'pos': (0.2, 6.1), 'size': (1.8, 0.6), 'text': '3个难度等级', 'parent': (2, 7), 'color': colors['method']},
        
        # 策略实现分支
        {'pos': (17.8, 8.5), 'size': (1.8, 0.6), 'text': '场景识别算法', 'parent': (16, 7), 'color': colors['method']},
        {'pos': (17.8, 7.7), 'size': (1.8, 0.6), 'text': '动态权重配置', 'parent': (16, 7), 'color': colors['method']},
        {'pos': (17.8, 6.9), 'size': (1.8, 0.6), 'text': '特殊规则保护', 'parent': (16, 7), 'color': colors['method']},
        {'pos': (17.8, 6.1), 'size': (1.8, 0.6), 'text': '增强评分机制', 'parent': (16, 7), 'color': colors['method']},
        
        # 实验验证分支
        {'pos': (1, 4.5), 'size': (2, 0.6), 'text': 'Weight-First 63%', 'parent': (4, 3), 'color': colors['result']},
        {'pos': (1, 3.7), 'size': (2, 0.6), 'text': 'Latest-First 56%', 'parent': (4, 3), 'color': colors['result']},
        {'pos': (1, 2.9), 'size': (2, 0.6), 'text': '自适应Hybrid 83%', 'parent': (4, 3), 'color': colors['result']},
        {'pos': (1, 2.1), 'size': (2, 0.6), 'text': '提升20%准确率', 'parent': (4, 3), 'color': colors['result']},
        
        # 结果分析分支
        {'pos': (17, 4.5), 'size': (2, 0.6), 'text': '场景识别有效', 'parent': (14, 3), 'color': colors['result']},
        {'pos': (17, 3.7), 'size': (2, 0.6), 'text': '动态权重优势', 'parent': (14, 3), 'color': colors['result']},
        {'pos': (17, 2.9), 'size': (2, 0.6), 'text': '极化配置有效', 'parent': (14, 3), 'color': colors['result']},
        {'pos': (17, 2.1), 'size': (2, 0.6), 'text': '真实数据验证', 'parent': (14, 3), 'color': colors['result']},
    ]
    
    # 绘制详细内容
    for detail in details:
        box = FancyBboxPatch(detail['pos'], detail['size'][0], detail['size'][1],
                            boxstyle="round,pad=0.02", 
                            facecolor=detail['color'], 
                            edgecolor='gray', linewidth=1, alpha=0.8)
        ax.add_patch(box)
        ax.text(detail['pos'][0] + detail['size'][0]/2, 
                detail['pos'][1] + detail['size'][1]/2, 
                detail['text'], 
                ha='center', va='center', fontsize=9, color='white')
        
        # 连接到父节点
        parent_x = detail['parent'][0] + 1.25  # 父节点中心x
        parent_y = detail['parent'][1] + 0.5   # 父节点中心y
        detail_x = detail['pos'][0] + detail['size'][0]/2
        detail_y = detail['pos'][1] + detail['size'][1]/2
        
        ax.plot([parent_x, detail_x], [parent_y, detail_y], 
                'k-', linewidth=1, alpha=0.5)
    
    # 添加核心洞察框
    insight_box = FancyBboxPatch((8, 0.5), 4, 1.2,
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['insight'], 
                                edgecolor='black', linewidth=2)
    ax.add_patch(insight_box)
    ax.text(10, 1.1, '核心洞察：场景适应性 > 复杂算法\n"不是所有冲突都应该用同样的方式解决"', 
            ha='center', va='center', fontsize=12, fontweight='bold', color='white')
    
    # 连接核心洞察到中心节点
    ax.plot([10, 10], [6.5, 1.7], 'k-', linewidth=3, alpha=0.8)
    
    # 添加流程箭头
    flow_arrows = [
        # 问题发现 → 实验设计
        {'start': (6.5, 11.5), 'end': (14, 11.5), 'label': '假设提出'},
        # 数据基础 → 策略实现  
        {'start': (4.5, 7.5), 'end': (16, 7.5), 'label': '策略设计'},
        # 实验设计 → 实验验证
        {'start': (14.5, 10), 'end': (5.5, 4), 'label': '实验执行'},
        # 实验验证 → 结果分析
        {'start': (6.5, 3.5), 'end': (14, 3.5), 'label': '结果评估'}
    ]
    
    for arrow in flow_arrows:
        ax.annotate('', xy=arrow['end'], xytext=arrow['start'],
                   arrowprops=dict(arrowstyle='->', lw=2, color='red', alpha=0.7))
        # 添加标签
        mid_x = (arrow['start'][0] + arrow['end'][0]) / 2
        mid_y = (arrow['start'][1] + arrow['end'][1]) / 2
        ax.text(mid_x, mid_y + 0.3, arrow['label'], 
                ha='center', va='center', fontsize=9, 
                bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
    
    # 添加标题
    ax.text(10, 13.5, '基于拉萨知识图谱的冲突解决策略实验思维导图', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        {'color': colors['problem'], 'label': '问题发现'},
        {'color': colors['design'], 'label': '实验设计'},
        {'color': colors['method'], 'label': '方法实现'},
        {'color': colors['result'], 'label': '结果验证'},
        {'color': colors['insight'], 'label': '核心洞察'}
    ]
    
    for i, elem in enumerate(legend_elements):
        y_pos = 13 - i * 0.3
        legend_box = FancyBboxPatch((0.5, y_pos-0.1), 0.3, 0.2,
                                   boxstyle="round,pad=0.02", 
                                   facecolor=elem['color'], 
                                   edgecolor='black', linewidth=1)
        ax.add_patch(legend_box)
        ax.text(1, y_pos, elem['label'], ha='left', va='center', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('experiment_mindmap.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def create_detailed_flow_chart():
    """创建详细的实验流程图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 定义流程步骤
    steps = [
        # 第一行：问题发现
        {'pos': (1, 10), 'size': (2.5, 1), 'text': '问题发现\n固定权重局限性', 'color': '#FFA07A'},
        {'pos': (4.5, 10), 'size': (2.5, 1), 'text': '现象分析\nLatest-First在\n困难场景更好', 'color': '#FFA07A'},
        {'pos': (8, 10), 'size': (2.5, 1), 'text': '根因分析\n过度重视权威性\n忽略时效性', 'color': '#FFA07A'},
        
        # 第二行：假设提出
        {'pos': (1, 8), 'size': (2.5, 1), 'text': '假设1\n场景适应性\n更重要', 'color': '#45B7D1'},
        {'pos': (4.5, 8), 'size': (2.5, 1), 'text': '假设2\n动态权重\n更有效', 'color': '#45B7D1'},
        {'pos': (8, 8), 'size': (2.5, 1), 'text': '假设3\n极化配置\n优于平衡', 'color': '#45B7D1'},
        
        # 第三行：数据准备
        {'pos': (1, 6), 'size': (2.5, 1), 'text': '数据基础\n260个真实\n拉萨景点', 'color': '#32CD32'},
        {'pos': (4.5, 6), 'size': (2.5, 1), 'text': '案例生成\n100个冲突\n测试案例', 'color': '#32CD32'},
        {'pos': (8, 6), 'size': (2.5, 1), 'text': '场景分类\n4种冲突类型\n3个难度等级', 'color': '#32CD32'},
        
        # 第四行：策略设计
        {'pos': (1, 4), 'size': (2.5, 1), 'text': '场景识别\n多维特征分析\n分层决策逻辑', 'color': '#9370DB'},
        {'pos': (4.5, 4), 'size': (2.5, 1), 'text': '动态权重\n6种场景配置\n极化权重分配', 'color': '#9370DB'},
        {'pos': (8, 4), 'size': (2.5, 1), 'text': '规则保护\n极端情况\n直接决策', 'color': '#9370DB'},
        
        # 第五行：实验验证
        {'pos': (1, 2), 'size': (2.5, 1), 'text': '对比实验\nWeight-First\nLatest-First\n自适应Hybrid', 'color': '#FFD700'},
        {'pos': (4.5, 2), 'size': (2.5, 1), 'text': '多维评估\n整体性能\n场景性能\n难度性能', 'color': '#FFD700'},
        {'pos': (8, 2), 'size': (2.5, 1), 'text': '结果验证\n83% vs 63% vs 56%\n提升20%准确率', 'color': '#FFD700'},
        
        # 右侧：核心原理
        {'pos': (12, 9), 'size': (3, 1.5), 'text': '核心原理\n场景感知 + 动态调整\n> 固定权重', 'color': '#FF6B6B'},
        {'pos': (12, 6.5), 'size': (3, 1.5), 'text': '关键创新\n智能场景识别\n极化权重配置\n规则算法结合', 'color': '#FF6B6B'},
        {'pos': (12, 4), 'size': (3, 1.5), 'text': '实际价值\n知识图谱冲突解决\n信息融合系统\n决策支持系统', 'color': '#FF6B6B'},
    ]
    
    # 绘制所有步骤
    for step in steps:
        box = FancyBboxPatch(step['pos'], step['size'][0], step['size'][1],
                            boxstyle="round,pad=0.05", 
                            facecolor=step['color'], 
                            edgecolor='black', linewidth=1.5, alpha=0.8)
        ax.add_patch(box)
        ax.text(step['pos'][0] + step['size'][0]/2, 
                step['pos'][1] + step['size'][1]/2, 
                step['text'], 
                ha='center', va='center', fontsize=10, fontweight='bold', color='white')
    
    # 添加流程箭头
    flow_connections = [
        # 横向连接
        [(2.25, 10.5), (4.5, 10.5)],  # 问题发现流程
        [(5.75, 10.5), (8, 10.5)],
        [(2.25, 8.5), (4.5, 8.5)],    # 假设提出流程
        [(5.75, 8.5), (8, 8.5)],
        [(2.25, 6.5), (4.5, 6.5)],    # 数据准备流程
        [(5.75, 6.5), (8, 6.5)],
        [(2.25, 4.5), (4.5, 4.5)],    # 策略设计流程
        [(5.75, 4.5), (8, 4.5)],
        [(2.25, 2.5), (4.5, 2.5)],    # 实验验证流程
        [(5.75, 2.5), (8, 2.5)],
        
        # 纵向连接
        [(2.25, 10), (2.25, 9)],      # 问题→假设
        [(2.25, 8), (2.25, 7)],       # 假设→数据
        [(2.25, 6), (2.25, 5)],       # 数据→策略
        [(2.25, 4), (2.25, 3)],       # 策略→验证
        
        # 连接到右侧原理
        [(10.5, 10.5), (12, 9.75)],   # 问题→原理
        [(10.5, 6.5), (12, 7.25)],    # 数据→创新
        [(10.5, 2.5), (12, 4.75)],    # 验证→价值
    ]
    
    for connection in flow_connections:
        start, end = connection
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.5, color='black', alpha=0.6))
    
    # 添加阶段标签
    stage_labels = [
        {'pos': (0.2, 10.5), 'text': '阶段1\n问题发现', 'color': '#FFA07A'},
        {'pos': (0.2, 8.5), 'text': '阶段2\n假设提出', 'color': '#45B7D1'},
        {'pos': (0.2, 6.5), 'text': '阶段3\n数据准备', 'color': '#32CD32'},
        {'pos': (0.2, 4.5), 'text': '阶段4\n策略设计', 'color': '#9370DB'},
        {'pos': (0.2, 2.5), 'text': '阶段5\n实验验证', 'color': '#FFD700'},
    ]
    
    for label in stage_labels:
        ax.text(label['pos'][0], label['pos'][1], label['text'], 
                ha='center', va='center', fontsize=9, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=label['color'], alpha=0.7))
    
    # 添加标题
    ax.text(8, 11.5, '实验流程与原理详细图', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('experiment_flow_chart.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return fig

def main():
    """主函数"""
    print("🎨 创建实验思维导图和流程图")
    print("=" * 50)
    
    # 创建思维导图
    print("📊 创建实验思维导图...")
    create_experiment_mindmap()
    print("✅ 思维导图已保存: experiment_mindmap.png")
    
    # 创建详细流程图
    print("📊 创建详细流程图...")
    create_detailed_flow_chart()
    print("✅ 流程图已保存: experiment_flow_chart.png")
    
    print(f"\n🎉 思维导图创建完成！")
    print(f"📁 生成的文件:")
    print(f"   • experiment_mindmap.png - 实验思维导图")
    print(f"   • experiment_flow_chart.png - 详细流程图")

if __name__ == "__main__":
    main()
