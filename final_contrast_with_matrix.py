#!/usr/bin/env python3
"""
最终版本：高对比度实验结果图 + 策略匹配矩阵

将策略-场景匹配矩阵作为第四个图，突出最佳策略选择
"""

import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import matplotlib.patches as patches

# 设置中文字体和高对比度样式
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def create_final_contrast_visualization():
    """创建最终版本的高对比度可视化"""
    
    # 实验结果数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 各场景下的表现
    weight_dominant = [1.000, 0.500, 1.000]
    time_dominant = [0.160, 1.000, 0.600]
    complex_tradeoff = [0.640, 0.360, 0.800]
    edge_case = [0.650, 0.350, 0.900]
    
    # 各难度下的表现
    easy_performance = [0.550, 0.400, 1.000]
    medium_performance = [0.722, 0.472, 0.917]
    hard_performance = [0.591, 0.705, 0.682]
    
    # 创建高对比度图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=20, fontweight='bold', y=0.96)
    
    # 高对比度颜色方案
    colors_main = ['#D32F2F', '#FF8F00', '#2E7D32']  # 深红、橙、深绿
    
    # 1. 整体准确率对比
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors_main, alpha=0.9, 
                    edgecolor='black', linewidth=2.5)
    ax1.set_title('整体准确率对比', fontsize=16, fontweight='bold', pad=15)
    ax1.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3, axis='y', linewidth=1.5)
    
    # 添加粗体数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', fontweight='bold', fontsize=13)
    
    # 2. 按冲突类型分析
    x = np.arange(len(strategies))
    width = 0.18
    
    colors_scenario = ['#B71C1C', '#E65100', '#1565C0', '#2E7D32']
    
    ax2.bar(x - 1.5*width, weight_dominant, width, label='权重主导', 
           color=colors_scenario[0], alpha=0.9, edgecolor='black', linewidth=1.5)
    ax2.bar(x - 0.5*width, time_dominant, width, label='时间主导', 
           color=colors_scenario[1], alpha=0.9, edgecolor='black', linewidth=1.5)
    ax2.bar(x + 0.5*width, complex_tradeoff, width, label='复杂权衡', 
           color=colors_scenario[2], alpha=0.9, edgecolor='black', linewidth=1.5)
    ax2.bar(x + 1.5*width, edge_case, width, label='边界情况', 
           color=colors_scenario[3], alpha=0.9, edgecolor='black', linewidth=1.5)
    
    ax2.set_title('按冲突类型的准确率分析', fontsize=16, fontweight='bold', pad=15)
    ax2.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(strategies, fontweight='bold')
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.set_ylim(0, 1.1)
    ax2.grid(True, alpha=0.3, axis='y', linewidth=1.5)
    
    # 3. 按难度等级分析
    colors_difficulty = ['#4CAF50', '#FF9800', '#F44336']
    
    ax3.bar(x - width, easy_performance, width, label='简单', 
           color=colors_difficulty[0], alpha=0.9, edgecolor='black', linewidth=1.5)
    ax3.bar(x, medium_performance, width, label='中等', 
           color=colors_difficulty[1], alpha=0.9, edgecolor='black', linewidth=1.5)
    ax3.bar(x + width, hard_performance, width, label='困难', 
           color=colors_difficulty[2], alpha=0.9, edgecolor='black', linewidth=1.5)
    
    ax3.set_title('按难度等级的准确率分析', fontsize=16, fontweight='bold', pad=15)
    ax3.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(strategies, fontweight='bold')
    ax3.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax3.set_ylim(0, 1.1)
    ax3.grid(True, alpha=0.3, axis='y', linewidth=1.5)
    
    # 4. 策略-场景最佳匹配矩阵
    create_enhanced_strategy_matrix(ax4)
    
    plt.tight_layout()
    plt.savefig('final_contrast_with_matrix.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    return fig

def create_enhanced_strategy_matrix(ax):
    """创建增强版策略-场景匹配矩阵"""
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单', '中等', '困难']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 性能数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],  # Weight-First
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],  # Latest-First
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]   # 自适应Hybrid
    ])
    
    # 确定每个场景的最佳策略
    best_strategy_indices = np.argmax(performance_data, axis=0)
    
    # 创建矩阵背景
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1, alpha=0.8)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold', fontsize=12)
    ax.set_yticklabels(strategies, fontweight='bold', fontsize=12)
    
    # 添加性能数值和最佳策略标记
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = performance_data[i, j]
            
            # 添加性能数值
            ax.text(j, i, f'{value:.2f}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=11)
            
            # 为最佳策略添加特殊标记
            if i == best_strategy_indices[j]:
                # 添加金色边框突出最佳策略
                rect = patches.Rectangle((j-0.45, i-0.45), 0.9, 0.9, 
                                       linewidth=4, edgecolor='gold', 
                                       facecolor='none', alpha=0.9)
                ax.add_patch(rect)
                
                # 添加皇冠图标
                ax.text(j+0.3, i-0.3, '👑', ha="center", va="center", fontsize=16)
    
    ax.set_title('策略-场景最佳匹配矩阵\n(👑 = 最佳策略，金框突出显示)', 
                fontsize=16, fontweight='bold', pad=20)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=20)
    cbar.set_label('准确率', fontweight='bold', fontsize=12)
    cbar.ax.tick_params(labelsize=10)
    
    # 添加图例说明
    legend_text = "图例：\n• 颜色深浅表示准确率高低\n• 👑 标记最佳策略\n• 金框突出最佳选择"
    ax.text(1.15, 0.5, legend_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", 
           facecolor="lightgray", alpha=0.8))

def create_standalone_matrix():
    """创建独立的大尺寸策略匹配矩阵"""
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况', '简单场景', '中等场景', '困难场景']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    # 性能数据
    performance_data = np.array([
        [1.000, 0.160, 0.640, 0.650, 0.550, 0.722, 0.591],  # Weight-First
        [0.500, 1.000, 0.360, 0.350, 0.400, 0.472, 0.705],  # Latest-First
        [1.000, 0.600, 0.800, 0.900, 1.000, 0.917, 0.682]   # 自适应Hybrid
    ])
    
    # 确定每个场景的最佳策略
    best_strategy_indices = np.argmax(performance_data, axis=0)
    
    # 创建热力图
    im = ax.imshow(performance_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1, alpha=0.9)
    
    # 设置标签
    ax.set_xticks(np.arange(len(scenarios)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(scenarios, rotation=45, ha='right', fontweight='bold', fontsize=14)
    ax.set_yticklabels(strategies, fontweight='bold', fontsize=14)
    
    # 添加网格线
    ax.set_xticks(np.arange(len(scenarios)+1)-.5, minor=True)
    ax.set_yticks(np.arange(len(strategies)+1)-.5, minor=True)
    ax.grid(which="minor", color="black", linestyle='-', linewidth=2)
    ax.tick_params(which="minor", size=0)
    
    # 添加性能数值和最佳策略标记
    for i in range(len(strategies)):
        for j in range(len(scenarios)):
            value = performance_data[i, j]
            
            # 添加性能数值
            ax.text(j, i, f'{value:.1%}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=13)
            
            # 为最佳策略添加特殊标记
            if i == best_strategy_indices[j]:
                # 添加金色边框
                rect = patches.Rectangle((j-0.48, i-0.48), 0.96, 0.96, 
                                       linewidth=5, edgecolor='gold', 
                                       facecolor='none', alpha=1.0)
                ax.add_patch(rect)
                
                # 添加皇冠和星星
                ax.text(j+0.35, i-0.35, '👑', ha="center", va="center", fontsize=20)
                ax.text(j-0.35, i+0.35, '⭐', ha="center", va="center", fontsize=16)
    
    # 设置标题
    ax.set_title('策略-场景最佳匹配矩阵\n基于拉萨知识图谱冲突解决实验结果', 
                fontsize=18, fontweight='bold', pad=30)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=30, pad=0.02)
    cbar.set_label('准确率', fontweight='bold', fontsize=14)
    cbar.ax.tick_params(labelsize=12)
    
    # 添加详细图例
    legend_elements = [
        "图例说明：",
        "• 颜色：绿色=高准确率，红色=低准确率",
        "• 👑：该场景下的最佳策略",
        "• ⭐：性能优异标记",
        "• 金框：突出最佳选择",
        "",
        "关键发现：",
        "• 自适应Hybrid在5/7场景中表现最佳",
        "• Latest-First在困难场景表现突出",
        "• Weight-First在权重主导场景完美"
    ]
    
    legend_text = "\n".join(legend_elements)
    ax.text(1.25, 0.5, legend_text, transform=ax.transAxes, fontsize=11,
           verticalalignment='center', bbox=dict(boxstyle="round,pad=0.5", 
           facecolor="lightblue", alpha=0.8, edgecolor="navy"))
    
    plt.tight_layout()
    plt.savefig('standalone_strategy_matrix.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def main():
    """主函数"""
    print("🎨 创建最终版本：高对比度图表 + 策略匹配矩阵")
    print("=" * 60)
    
    # 创建主图（包含匹配矩阵作为第四个图）
    print("📊 创建高对比度主图（含策略匹配矩阵）...")
    create_final_contrast_visualization()
    print("✅ 主图已保存: final_contrast_with_matrix.png")
    
    # 创建独立的大尺寸匹配矩阵
    print("📊 创建独立的策略匹配矩阵...")
    create_standalone_matrix()
    print("✅ 独立矩阵已保存: standalone_strategy_matrix.png")
    
    print(f"\n🎉 可视化完成！")
    print(f"📁 生成的文件:")
    print(f"   • final_contrast_with_matrix.png - 完整的四图对比（含匹配矩阵）")
    print(f"   • standalone_strategy_matrix.png - 独立的大尺寸策略匹配矩阵")
    
    print(f"\n💡 策略匹配矩阵的优势:")
    print(f"   ✅ 直观显示每个场景的最佳策略选择")
    print(f"   ✅ 金框和皇冠标记突出最佳选择")
    print(f"   ✅ 颜色编码显示性能差异")
    print(f"   ✅ 一目了然的决策指导价值")
    
    print(f"\n📈 关键发现:")
    print(f"   • 自适应Hybrid在7个场景中的5个表现最佳")
    print(f"   • Latest-First在困难场景表现突出")
    print(f"   • Weight-First在权重主导场景完美表现")

if __name__ == "__main__":
    main()
