#!/usr/bin/env python3
"""
简化版增强对比度可视化
"""

import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_enhanced_experiment_results():
    """创建增强对比度的实验结果图"""
    
    # 实验数据
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    overall_accuracy = [0.630, 0.560, 0.830]
    
    # 场景性能数据
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况']
    performance_data = {
        'Weight-First': [1.000, 0.160, 0.640, 0.650],
        'Latest-First': [0.500, 1.000, 0.360, 0.350],
        '自适应Hybrid': [1.000, 0.600, 0.800, 0.900]
    }
    
    # 难度性能数据
    difficulties = ['简单', '中等', '困难']
    difficulty_data = {
        'Weight-First': [0.550, 0.722, 0.591],
        'Latest-First': [0.400, 0.472, 0.705],
        '自适应Hybrid': [1.000, 0.917, 0.682]
    }
    
    # 高对比度颜色
    colors = ['#D32F2F', '#1976D2', '#388E3C']  # 深红、深蓝、深绿
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('基于拉萨知识图谱的冲突解决策略对比实验', fontsize=18, fontweight='bold', y=0.98)
    
    # 1. 整体性能对比
    bars1 = ax1.bar(strategies, overall_accuracy, color=colors, alpha=0.9, 
                    edgecolor='black', linewidth=2)
    ax1.set_title('整体准确率对比', fontsize=16, fontweight='bold')
    ax1.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax1.set_ylim(0, 1.0)
    ax1.grid(True, alpha=0.3, linestyle='--')
    
    # 添加数值标签
    for bar, acc in zip(bars1, overall_accuracy):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    # 2. 场景性能对比
    x = np.arange(len(scenarios))
    width = 0.25
    
    for i, (strategy, color) in enumerate(zip(strategies, colors)):
        offset = (i - 1) * width
        bars = ax2.bar(x + offset, performance_data[strategy], width, 
                      label=strategy, color=color, alpha=0.9, 
                      edgecolor='black', linewidth=1)
    
    ax2.set_title('各场景类型性能对比', fontsize=16, fontweight='bold')
    ax2.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax2.set_xlabel('场景类型', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(scenarios, fontweight='bold')
    ax2.legend(fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3, linestyle='--')
    ax2.set_ylim(0, 1.1)
    
    # 3. 难度性能对比
    x = np.arange(len(difficulties))
    
    for i, (strategy, color) in enumerate(zip(strategies, colors)):
        offset = (i - 1) * width
        bars = ax3.bar(x + offset, difficulty_data[strategy], width,
                      label=strategy, color=color, alpha=0.9,
                      edgecolor='black', linewidth=1)
    
    ax3.set_title('各难度等级性能对比', fontsize=16, fontweight='bold')
    ax3.set_ylabel('准确率', fontsize=14, fontweight='bold')
    ax3.set_xlabel('难度等级', fontsize=14, fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels(difficulties, fontweight='bold')
    ax3.legend(fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3, linestyle='--')
    ax3.set_ylim(0, 1.1)
    
    return fig, ax4

def create_radar_chart(ax):
    """创建策略能力雷达图"""
    
    # 能力维度
    categories = ['权威性识别', '时效性识别', '复杂权衡', '边界处理', '整体稳定性']
    
    # 各策略评分 (0-1)
    weight_first = [1.0, 0.2, 0.6, 0.6, 0.7]
    latest_first = [0.3, 1.0, 0.4, 0.4, 0.6]
    adaptive = [1.0, 0.8, 0.9, 0.9, 0.8]
    
    # 角度计算
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    # 数据闭合
    weight_first += weight_first[:1]
    latest_first += latest_first[:1]
    adaptive += adaptive[:1]
    
    # 清除并重新创建为极坐标
    ax.clear()
    ax = plt.subplot(2, 2, 4, projection='polar')
    
    # 绘制雷达图
    ax.plot(angles, weight_first, 'o-', linewidth=3, label='Weight-First', 
            color='#D32F2F', markersize=6)
    ax.fill(angles, weight_first, alpha=0.2, color='#D32F2F')
    
    ax.plot(angles, latest_first, 's-', linewidth=3, label='Latest-First', 
            color='#1976D2', markersize=6)
    ax.fill(angles, latest_first, alpha=0.2, color='#1976D2')
    
    ax.plot(angles, adaptive, '^-', linewidth=4, label='自适应Hybrid', 
            color='#388E3C', markersize=8)
    ax.fill(angles, adaptive, alpha=0.3, color='#388E3C')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=11, fontweight='bold')
    ax.set_ylim(0, 1)
    ax.set_title('策略能力雷达图', fontsize=16, fontweight='bold', y=1.08)
    ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0), fontsize=11, fontweight='bold')
    ax.grid(True, alpha=0.3)

def create_improvement_chart(ax):
    """创建性能提升图"""
    
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    baseline = 0.630
    improvements = [0, 0.560 - baseline, 0.830 - baseline]
    
    colors = ['#757575', '#D32F2F', '#388E3C']
    
    bars = ax.bar(strategies, improvements, color=colors, alpha=0.9,
                  edgecolor='black', linewidth=2)
    ax.set_title('相比Weight-First的性能变化', fontsize=16, fontweight='bold')
    ax.set_ylabel('准确率变化', fontsize=14, fontweight='bold')
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=2)
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 添加数值标签
    for bar, improvement in zip(bars, improvements):
        height = bar.get_height()
        if height >= 0:
            label = f'+{improvement:.1%}' if improvement > 0 else '基准'
            y_pos = height + 0.005
            va = 'bottom'
        else:
            label = f'{improvement:.1%}'
            y_pos = height - 0.005
            va = 'top'
        
        ax.text(bar.get_x() + bar.get_width()/2., y_pos, label,
                ha='center', va=va, fontsize=14, fontweight='bold')

def create_matrix_heatmap(ax):
    """创建策略-场景性能矩阵"""
    
    scenarios = ['权重主导', '时间主导', '复杂权衡', '边界情况']
    strategies = ['Weight-First', 'Latest-First', '自适应Hybrid']
    
    data = np.array([
        [1.000, 0.500, 1.000],
        [0.160, 1.000, 0.600],
        [0.640, 0.360, 0.800],
        [0.650, 0.350, 0.900]
    ])
    
    im = ax.imshow(data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    ax.set_title('策略-场景性能矩阵', fontsize=16, fontweight='bold')
    ax.set_xticks(range(len(strategies)))
    ax.set_xticklabels(strategies, fontweight='bold')
    ax.set_yticks(range(len(scenarios)))
    ax.set_yticklabels(scenarios, fontweight='bold')
    
    # 添加数值
    for i in range(len(scenarios)):
        for j in range(len(strategies)):
            value = data[i, j]
            color = 'white' if value < 0.5 else 'black'
            ax.text(j, i, f'{value:.2f}', ha="center", va="center",
                   color=color, fontsize=12, fontweight='bold')

def main():
    """生成四种不同的第四个图方案"""
    
    print("🎨 生成增强对比度的实验结果可视化")
    
    # 方案1：雷达图
    print("📊 生成方案1：策略能力雷达图...")
    fig1, ax4 = create_enhanced_experiment_results()
    create_radar_chart(ax4)
    plt.tight_layout()
    plt.savefig('enhanced_results_radar.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 方案2：性能提升图
    print("📊 生成方案2：性能提升对比图...")
    fig2, ax4 = create_enhanced_experiment_results()
    create_improvement_chart(ax4)
    plt.tight_layout()
    plt.savefig('enhanced_results_improvement.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 方案3：矩阵热力图
    print("📊 生成方案3：策略-场景性能矩阵...")
    fig3, ax4 = create_enhanced_experiment_results()
    create_matrix_heatmap(ax4)
    plt.tight_layout()
    plt.savefig('enhanced_results_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 完成！生成了3种增强对比度的可视化方案")
    print("📁 文件:")
    print("   • enhanced_results_radar.png - 策略能力雷达图")
    print("   • enhanced_results_improvement.png - 性能提升对比图") 
    print("   • enhanced_results_matrix.png - 策略-场景性能矩阵")

if __name__ == "__main__":
    main()
